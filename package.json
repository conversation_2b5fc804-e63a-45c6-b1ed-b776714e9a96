{"name": "<PERSON><PERSON><PERSON>-monore<PERSON>", "version": "0.1.0", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "npm --prefix packages/web run dev", "build": "npm --prefix packages/web run build", "start": "npm --prefix packages/web run start", "lint": "npm --prefix packages/web run lint", "lint:biome": "biome lint .", "lint:biome:fix": "biome lint --write .", "format": "biome format --write .", "format:check": "biome format .", "check": "biome check .", "check:fix": "biome check --write .", "check:no-imports": "biome lint . && biome format .", "check:fix:no-imports": "biome lint --write . && biome format --write .", "hooks:install": "lefthook install", "hooks:uninstall": "lefthook uninstall", "test": "npm --prefix packages/web run test", "test:ui": "npm --prefix packages/web run test:ui", "test:run": "npm --prefix packages/web run test:run", "test:coverage": "npm --prefix packages/web run test:coverage", "postinstall": "npm --prefix packages/web run postinstall", "install:clean": "rm -rf node_modules package-lock.json packages/*/node_modules packages/*/package-lock.json && npm install", "type-check": "npm --prefix packages/web run type-check", "db:generate": "npm --prefix packages/web run db:generate", "db:push": "npm --prefix packages/web run db:push", "db:studio": "npm --prefix packages/web run db:studio", "db:migrate": "npm --prefix packages/web run db:migrate", "db:migrate:deploy": "npm --prefix packages/web run db:migrate:deploy", "db:migrate:reset": "npm --prefix packages/web run db:migrate:reset", "db:seed": "npm --prefix packages/web run db:seed", "lingui:extract": "npm --prefix packages/web run lingui:extract", "lingui:compile": "npm --prefix packages/web run lingui:compile"}, "devDependencies": {"@biomejs/biome": "^2.1.3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "24.1.0", "lefthook": "^1.12.2", "postcss-cli": "^11.0.1", "typescript": "^5.8.3"}, "dependencies": {"fumadocs-core": "^15.6.8", "fumadocs-mdx": "^11.7.3", "fumadocs-ui": "^15.6.8", "postcss-import": "^16.1.1"}}