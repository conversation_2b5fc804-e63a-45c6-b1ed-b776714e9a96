---
title: State Management Data Fetching
id: cmdwfdj630001jm04dehfeb4d
name: State Management Data Fetching
description: State Management Data Fetching Rules, It describe How to fetch data, and simpify logic
author: <PERSON><PERSON><PERSON> Z<PERSON>
updatedAt: 2025-08-04T01:22:56.709Z
applyType: auto
glob: *.tsx,*.ts
tags: []
---

# State Management Data Fetching

> State Management Data Fetching Rules, It describe How to fetch data, and simpify logic



## Rule Content

Use @tanstack/react-query for DataFetch , Invalidate By queryId after mutation

```
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from '@tanstack/react-query'

const queryClient = new QueryClient()

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Example />
    </QueryClientProvider>
  )
}

function Example() {
  const { isPending, error, data } = useQuery({
    queryKey: ['repoData'],
    queryFn: () =>
      fetch('https://api.github.com/repos/TanStack/query').then((res) =>
        res.json(),
      ),
  })
```