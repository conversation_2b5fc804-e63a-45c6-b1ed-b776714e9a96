# Lefthook + Biome Integration

This project uses [Lefthook](https://lefthook.dev/) to automatically run Biome linting and formatting on git commits.

## How it works

- **Pre-commit**: Automatically runs `biome lint --write` and `biome format --write` on staged files
- **Pre-push**: Runs `biome check .` on the entire codebase

## Commands

- `bun run hooks:install` - Install git hooks
- `bun run hooks:uninstall` - Remove git hooks
- `bunx lefthook run pre-commit` - Manually run pre-commit checks
- `bunx lefthook run pre-push` - Manually run pre-push checks

## Configuration

The Lefthook configuration is in `lefthook.yml` at the project root.

## What happens on commit

1. Lefthook runs automatically when you commit
2. It formats and lints your staged files
3. If there are linting errors, the commit is blocked
4. If there are formatting issues, they're automatically fixed and you need to re-stage the changes

## Bypassing hooks (not recommended)

If you need to bypass the hooks in an emergency:
```bash
git commit --no-verify -m "emergency commit"
```
