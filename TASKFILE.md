# OnlyRules Taskfile Documentation

This project uses [Task](https://taskfile.dev/) as a centralized command runner to provide a consistent, discoverable interface for all development operations.

## Installation

First, install Task if you haven't already:

### macOS (Homebrew)
```bash
brew install go-task/tap/go-task
```

### Linux/Windows
```bash
# Using Go
go install github.com/go-task/task/v3/cmd/task@latest

# Or download binary from https://github.com/go-task/task/releases
```

## Quick Start

```bash
# Show all available tasks
task --list

# Start development server
task dev

# Run tests
task test:run

# Build for production
task build

# Complete project setup for new developers
task setup
```

## Task Categories

### 🚀 Development Tasks
- `task dev` - Start development server
- `task dev:fresh` - Fresh start (clean install + setup + dev)
- `task dev:full` - Start with database and all services
- `task start` - Start production server

### 🔨 Build Tasks
- `task build` - Production build
- `task build:clean` - Clean and rebuild
- `task type-check` - TypeScript type checking
- `task lint` - Code linting
- `task lint:fix` - Auto-fix linting issues

### 🗄️ Database Tasks
- `task db:generate` - Generate Prisma client
- `task db:migrate` - Run migrations (dev)
- `task db:migrate:deploy` - Deploy migrations (prod)
- `task db:seed` - Seed database
- `task db:studio` - Open Prisma Studio
- `task db:setup` - Complete database setup

### 🧪 Testing Tasks
- `task test` - Run tests in watch mode
- `task test:run` - Run all tests once
- `task test:coverage` - Run with coverage
- `task test:ui` - Run with UI interface
- `task test:ci` - CI-friendly test run

### 🌍 Internationalization
- `task i18n:extract` - Extract translatable strings
- `task i18n:compile` - Compile translations
- `task i18n:update` - Extract and compile

### 🚀 Deployment Tasks
- `task deploy:verify` - Verify build readiness
- `task deploy:build` - Full deployment build
- `task ci` - Run all CI checks

### 🛠️ Utility Tasks
- `task clean` - Clean build artifacts
- `task install` - Install dependencies
- `task install:clean` - Clean install
- `task format` - Format code
- `task setup` - Complete project setup

### 🔍 Maintenance Tasks
- `task health-check` - Check project health
- `task security:audit` - Security audit
- `task update:deps` - Check for outdated packages
- `task verify:migration` - Verify Radix UI migration

### ⚡ Quick Aliases
- `task d` - Quick dev
- `task b` - Quick build
- `task t` - Quick test
- `task l` - Quick lint

## Common Workflows

### New Developer Setup
```bash
# Complete setup for new developers
task setup
```

### Daily Development
```bash
# Start development
task dev

# Run tests while developing
task test

# Before committing
task pre-commit
```

### Before Deployment
```bash
# Verify everything is ready
task deploy:verify

# Or full deployment build
task deploy:build
```

### Troubleshooting
```bash
# Clean everything and start fresh
task clean
task install:clean
task setup

# Check project health
task health-check
```

## Environment Variables

The Taskfile respects the following environment variables:

- `NODE_ENV` - Set environment (development/production)
- Standard npm/Node.js environment variables

## Monorepo Structure

This project is organized as a monorepo:
- Root: Contains shared configuration and orchestration
- `packages/web`: Main Next.js application
- `packages/shared`: Shared utilities and types

The Taskfile automatically handles the monorepo structure and runs commands in the appropriate directories.

## Task Dependencies

Tasks can have dependencies that run automatically:

- `build` depends on `type-check` and `db:generate`
- `start` depends on `build`
- `db:seed` depends on `db:generate`
- `deploy:verify` runs comprehensive checks

## Customization

To add new tasks, edit the `Taskfile.yml` file. Follow the existing patterns:

```yaml
new-task:
  desc: "Description of what this task does"
  deps: [dependency-task]  # Optional dependencies
  cmds:
    - command to run
    - another command
```

## Tips

1. **Use `task --list`** to see all available tasks
2. **Use `task --summary <task-name>`** to see task details
3. **Tasks run in parallel** when possible for better performance
4. **Dependencies are cached** - tasks only run when needed
5. **Use quick aliases** (`task d`, `task b`, etc.) for common operations

## Integration with IDEs

Most IDEs support Task integration:

- **VS Code**: Install the "Task Runner" extension
- **IntelliJ/WebStorm**: Built-in support for Task
- **Vim/Neovim**: Use task-specific plugins

This provides a consistent development experience across different editors and team members.
