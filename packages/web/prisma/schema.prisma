
generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  rules    Rule[]
  rulesets Ruleset[]
  sessions Session[]

  emailVerified Boolean
  image         String?
  accounts      Account[]

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  updatedAt DateTime
  ipAddress String?
  userAgent String?

  @@map("sessions")
}

model Rule {
  id          String     @id @default(cuid())
  title       String
  description String?
  content     String
  visibility  Visibility @default(PRIVATE)
  applyType   ApplyType  @default(manual)
  glob        String?
  shareToken  String?    @unique
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  userId      String

  user     User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  tags     RuleTag[]
  rulesets RulesetRule[]

  @@map("rules")
}

model Ruleset {
  id          String     @id @default(cuid())
  name        String
  description String?
  visibility  Visibility @default(PRIVATE)
  shareToken  String?    @unique
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  userId      String

  user  User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  rules RulesetRule[]

  @@map("rulesets")
}

model RulesetRule {
  rulesetId String
  ruleId    String
  order     Int      @default(0)
  createdAt DateTime @default(now())

  ruleset Ruleset @relation(fields: [rulesetId], references: [id], onDelete: Cascade)
  rule    Rule    @relation(fields: [ruleId], references: [id], onDelete: Cascade)

  @@id([rulesetId, ruleId])
  @@map("ruleset_rules")
}

model Tag {
  id    String @id @default(cuid())
  name  String @unique
  color String @default("#3B82F6")

  rules RuleTag[]

  @@map("tags")
}

model RuleTag {
  ruleId String
  tagId  String

  rule Rule @relation(fields: [ruleId], references: [id], onDelete: Cascade)
  tag  Tag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([ruleId, tagId])
  @@map("rule_tags")
}



enum Visibility {
  PRIVATE
  PUBLIC
}

enum ApplyType {
  auto
  manual
  always
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}
