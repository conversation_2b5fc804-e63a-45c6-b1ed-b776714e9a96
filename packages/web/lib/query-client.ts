import { QueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

// Create a query client with default options
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // How long data is considered fresh (5 minutes)
      staleTime: 5 * 60 * 1000,
      // How long data is cached (10 minutes)
      gcTime: 10 * 60 * 1000,
      // Retry failed requests
      retry: (failureCount, error: unknown) => {
        // Don't retry on 4xx errors (client errors)
        const errorWithStatus = error as { status?: number };
        if (
          errorWithStatus &&
          typeof errorWithStatus.status === 'number' &&
          errorWithStatus.status >= 400 &&
          errorWithStatus.status < 500
        ) {
          return false;
        }
        // Retry up to 3 times for server errors
        return failureCount < 3;
      },
      // Refetch on window focus (useful for real-time data)
      refetchOnWindowFocus: false,
      // Error handling
      throwOnError: false,
    },
    mutations: {
      retry: false,
      // Global error handler for mutations
      onError: (error: unknown) => {
        console.error('Mutation error:', error);
        const message = (error as { message?: string })?.message || 'An error occurred';
        toast.error(message);
      },
    },
  },
});

// Query keys factory for consistent key management
export const queryKeys = {
  // Rules
  rules: {
    all: ['rules'] as const,
    lists: () => [...queryKeys.rules.all, 'list'] as const,
    list: (filters: Record<string, unknown>) => [...queryKeys.rules.lists(), filters] as const,
    details: () => [...queryKeys.rules.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.rules.details(), id] as const,
    raw: (id: string) => [...queryKeys.rules.all, 'raw', id] as const,
  },
  // Rulesets
  rulesets: {
    all: ['rulesets'] as const,
    lists: () => [...queryKeys.rulesets.all, 'list'] as const,
    list: (filters: Record<string, unknown>) => [...queryKeys.rulesets.lists(), filters] as const,
    details: () => [...queryKeys.rulesets.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.rulesets.details(), id] as const,
  },
  // Tags
  tags: {
    all: ['tags'] as const,
    lists: () => [...queryKeys.tags.all, 'list'] as const,
  },
  // User data
  user: {
    all: ['user'] as const,
    profile: (username: string) => [...queryKeys.user.all, 'profile', username] as const,
  },
} as const;
