// Radix UI Theme v3 Configuration
// This file contains the theme configuration for consistent styling across the app

export const themeConfig = {
  // Theme appearance
  defaultTheme: 'dark' as const,

  // Color configuration
  accentColor: 'blue' as const,
  grayColor: 'slate' as const,

  // Visual properties
  radius: 'medium' as const,
  scaling: '100%' as const,
  panelBackground: 'translucent' as const,

  // Layout defaults
  hasBackground: false,

  // Custom color palette (if needed)
  customColors: {
    // Add custom color definitions here
  },

  // Component defaults
  components: {
    button: {
      defaultVariant: 'solid' as const,
      defaultSize: '2' as const,
    },
    card: {
      defaultVariant: 'surface' as const,
      defaultSize: '2' as const,
    },
    dialog: {
      defaultSize: '3' as const,
    },
  },

  // Breakpoints (matching Radix UI defaults)
  breakpoints: {
    initial: '0px',
    xs: '520px',
    sm: '768px',
    md: '1024px',
    lg: '1280px',
    xl: '1640px',
  },

  // Spacing scale
  space: {
    1: '4px',
    2: '8px',
    3: '12px',
    4: '16px',
    5: '24px',
    6: '32px',
    7: '40px',
    8: '48px',
    9: '64px',
  },

  // Font sizes
  fontSize: {
    1: '12px',
    2: '14px',
    3: '16px',
    4: '18px',
    5: '20px',
    6: '24px',
    7: '28px',
    8: '35px',
    9: '60px',
  },
} as const;

export type ThemeConfig = typeof themeConfig;
