import { IDE_METADATA, type IDEPreference, type IDEType } from './store';

/**
 * Generate an npx command for multiple IDEs and rule URL
 */
export function generateIDECommand(
  ruleUrl: string,
  ideTypes: IDEType[],
  mode: 'append' | 'init'
): string {
  const ideCommands = ideTypes.map((type) => IDE_METADATA[type].command).join(',');
  return `npx onlyrules ${mode} -f "${ruleUrl}" --target ${ideCommands}`;
}

/**
 * Generate the basic npx command without IDE target (current behavior)
 */
export function generateBasicCommand(ruleUrl: string): string {
  return `npx onlyrules init -f "${ruleUrl}"`;
}

/**
 * Get the display name for an IDE type
 */
export function getIDEDisplayName(ideType: IDEType): string {
  return IDE_METADATA[ideType].name;
}

/**
 * Get the description for an IDE type
 */
export function getIDEDescription(ideType: IDEType): string {
  return IDE_METADATA[ideType].description;
}

/**
 * Check if an IDE type is valid
 */
export function isValidIDEType(ideType: string): ideType is IDEType {
  return ideType in IDE_METADATA;
}

/**
 * Get all available IDE types
 */
export function getAllIDETypes(): IDEType[] {
  return Object.keys(IDE_METADATA) as IDEType[];
}

/**
 * Sort IDE preferences with default first
 */
export function sortIDEPreferences(preferences: IDEPreference[]): IDEPreference[] {
  return [...preferences].sort((a, b) => {
    // Default IDE comes first
    if (a.isDefault) return -1;
    if (b.isDefault) return 1;

    // Then sort alphabetically by name
    return a.name.localeCompare(b.name);
  });
}

/**
 * Generate commands for all preferred IDEs
 */
export function generateAllIDECommands(
  ruleUrl: string,
  preferences: IDEPreference[],
  mode: 'append' | 'init'
): string {
  return generateIDECommand(
    ruleUrl,
    preferences.map((r) => r.type),
    mode
  );
}

/**
 * Get the default IDE from preferences
 */
export function getDefaultIDE(preferences: IDEPreference[]): IDEPreference | undefined {
  return preferences.find((ide) => ide.isDefault);
}

/**
 * Copy text to clipboard with error handling
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
}

/**
 * Format IDE command for display (truncate if too long)
 */
export function formatCommandForDisplay(command: string, maxLength: number = 60): string {
  if (command.length <= maxLength) return command;
  return `${command.substring(0, maxLength - 3)}...`;
}
