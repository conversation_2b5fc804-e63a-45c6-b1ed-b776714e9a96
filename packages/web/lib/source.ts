// Simple mock source for build compatibility
import type React from 'react';
export const source = {
  pageTree: [
    {
      type: 'page',
      name: 'Documentation',
      url: '/docs',
      children: [
        {
          type: 'page',
          name: 'Getting Started',
          url: '/docs/getting-started',
          children: [
            {
              type: 'page',
              name: 'Quick Start',
              url: '/docs/getting-started/quick-start',
              children: [],
            },
          ],
        },
        {
          type: 'page',
          name: 'IDE Guides',
          url: '/docs/ides',
          children: [
            {
              type: 'page',
              name: '<PERSON>urs<PERSON>',
              url: '/docs/ides/cursor',
              children: [],
            },
          ],
        },
        {
          type: 'page',
          name: 'API Reference',
          url: '/docs/api',
          children: [
            {
              type: 'page',
              name: 'Overview',
              url: '/docs/api/overview',
              children: [],
            },
          ],
        },
        {
          type: 'page',
          name: 'Guides',
          url: '/docs/guides',
          children: [
            {
              type: 'page',
              name: 'SEO Setup',
              url: '/docs/guides/seo-setup',
              children: [],
            },
            {
              type: 'page',
              name: 'Internationalization',
              url: '/docs/guides/i18n',
              children: [],
            },
            {
              type: 'page',
              name: 'Radix UI Theme',
              url: '/docs/guides/radix-ui-theme',
              children: [],
            },
            {
              type: 'page',
              name: 'Theme Refactoring',
              url: '/docs/guides/theme-refactoring',
              children: [],
            },
          ],
        },
      ],
    },
  ],
  getPage: (slug: string[] | undefined) => {
    const { DocsContent } = require('./docs-content.tsx');
    const path = slug ? slug.join('/') : 'index';

    const pageData: Record<
      string,
      { title: string; description: string; body: React.ComponentType }
    > = {
      index: {
        title: 'Welcome to OnlyRules Documentation',
        description: 'Learn how to use OnlyRules AI Prompt Management Platform',
        body: DocsContent.index,
      },
      'getting-started/quick-start': {
        title: 'Quick Start Guide',
        description: 'Get started with OnlyRules in just a few minutes',
        body: DocsContent['getting-started/quick-start'],
      },
      'ides/cursor': {
        title: 'Cursor IDE Integration',
        description: 'Learn how to use OnlyRules with Cursor IDE',
        body: DocsContent['ides/cursor'],
      },
      'api/overview': {
        title: 'API Overview',
        description: 'Learn about the OnlyRules API endpoints and how to use them',
        body: DocsContent['api/overview'],
      },
      'guides/seo-setup': {
        title: 'SEO Setup for OnlyRules',
        description:
          'Learn how to configure SEO enhancements for the OnlyRules Next.js application',
        body: DocsContent['guides/seo-setup'],
      },
      'guides/i18n': {
        title: 'Internationalization (i18n) Guide',
        description: 'Learn how to use Lingui.js for internationalization in OnlyRules',
        body: DocsContent['guides/i18n'],
      },
      'guides/radix-ui-theme': {
        title: 'Radix UI Theme v3 Guide',
        description: 'Learn how to use Radix UI Theme v3 in the OnlyRules project',
        body: DocsContent['guides/radix-ui-theme'],
      },
      'guides/theme-refactoring': {
        title: 'Theme Refactoring Documentation',
        description:
          'Learn about the theme system refactoring and how to use the new unified theme architecture',
        body: DocsContent['guides/theme-refactoring'],
      },
    };

    const page = pageData[path];
    if (!page) return null;

    return {
      data: {
        title: page.title,
        description: page.description,
        body: page.body,
        toc: [],
        full: false,
        structuredData: {
          title: page.title,
          description: page.description,
        },
      },
    };
  },
  generateParams: () => {
    return [
      { slug: ['index'] },
      { slug: ['getting-started', 'quick-start'] },
      { slug: ['ides', 'cursor'] },
      { slug: ['api', 'overview'] },
      { slug: ['guides', 'seo-setup'] },
      { slug: ['guides', 'i18n'] },
      { slug: ['guides', 'radix-ui-theme'] },
      { slug: ['guides', 'theme-refactoring'] },
    ];
  },
  getPages: () => {
    return [
      { slug: ['index'] },
      { slug: ['getting-started', 'quick-start'] },
      { slug: ['ides', 'cursor'] },
      { slug: ['api', 'overview'] },
      { slug: ['guides', 'seo-setup'] },
      { slug: ['guides', 'i18n'] },
      { slug: ['guides', 'radix-ui-theme'] },
      { slug: ['guides', 'theme-refactoring'] },
    ];
  },
};
