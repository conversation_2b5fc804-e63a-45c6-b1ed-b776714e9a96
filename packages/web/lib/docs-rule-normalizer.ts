/**
 * Utilities for normalizing MDC files from packages/docs/rules to Rule entities
 */

import { readdirSync, readFileSync } from 'node:fs';
import { join } from 'node:path';
import type { Rule } from './store';

/**
 * Interface for parsed MDC frontmatter
 */
interface MDCFrontmatter {
  title?: string;
  description?: string;
  author?: string;
  tags?: string[];
  [key: string]: unknown;
}

/**
 * Parse frontmatter from MDC content
 */
function parseFrontmatter(content: string): {
  metadata: MDCFrontmatter;
  content: string;
} {
  const frontmatterRegex = /^\s*---\s*\r?\n([\s\S]*?)\r?\n---\s*\r?\n([\s\S]*)$/;
  const match = content.match(frontmatterRegex);

  if (!match) {
    return { metadata: {}, content: content.trim() };
  }

  const [, frontmatterStr, markdownContent] = match;
  const metadata: MDCFrontmatter = {};

  // Parse YAML-like frontmatter
  const lines = frontmatterStr.split(/\r?\n/);
  for (const line of lines) {
    const trimmedLine = line.trim();
    // Skip empty lines and comments
    if (!trimmedLine || trimmedLine.startsWith('#')) continue;

    const colonIndex = trimmedLine.indexOf(':');
    if (colonIndex === -1) continue;

    const key = trimmedLine.slice(0, colonIndex).trim();
    let value = trimmedLine.slice(colonIndex + 1).trim();

    // Skip if no value
    if (!value) continue;

    // Handle arrays (tags)
    if (value.startsWith('[') && value.endsWith(']')) {
      const arrayContent = value.slice(1, -1);
      const items = arrayContent
        .split(',')
        .map((item) => item.trim().replace(/^["']|["']$/g, ''))
        .filter(Boolean);
      metadata[key] = items;
      continue;
    }

    // Remove quotes if present
    if (
      (value.startsWith('"') && value.endsWith('"')) ||
      (value.startsWith("'") && value.endsWith("'"))
    ) {
      value = value.slice(1, -1);
    }

    metadata[key] = value;
  }

  return { metadata, content: markdownContent.trim() };
}

/**
 * Generate a unique ID for a rule based on filename
 */
function generateRuleId(filename: string): string {
  const baseName = filename.replace(/\.mdc$/, '');
  return `docs_${baseName}_${Date.now()}`;
}

/**
 * Normalize a single MDC file to Rule entity
 */
export function normalizeMDCToRule(filePath: string, content: string): Rule {
  const { metadata, content: ruleContent } = parseFrontmatter(content);
  const filename = filePath.split('/').pop() || 'unknown.mdc';

  const now = new Date().toISOString();
  const ruleId = generateRuleId(filename);

  return {
    id: ruleId,
    title: metadata.title || filename.replace(/\.mdc$/, '').replace(/[-_]/g, ' '),
    description: metadata.description || null,
    content: ruleContent,
    visibility: 'PUBLIC' as const,
    applyType: 'manual' as const,
    glob: undefined,
    shareToken: null,
    createdAt: now,
    updatedAt: now,
    userId: 'system', // System-generated rules
    tags: (metadata.tags || []).map((tagName: string) => ({
      tag: {
        id: `tag_${tagName.toLowerCase().replace(/\s+/g, '_')}`,
        name: tagName,
        color: '#3B82F6',
      },
    })),
    user: {
      id: 'system',
      name: metadata.author || 'System',
      email: '<EMAIL>',
      image: null,
      avatar: null,
    },
  };
}

/**
 * Read and normalize all MDC files from packages/docs/rules directory
 */
export function getAllDocsRules(): Rule[] {
  // Determine the correct path to packages/docs/rules
  // If we're in packages/web, go up two levels, otherwise assume we're at root
  const cwd = process.cwd();
  const isInWebPackage = cwd.includes('packages/web');
  const rulesDir = isInWebPackage
    ? join(cwd, '../../packages/docs/rules')
    : join(cwd, 'packages/docs/rules');

  try {
    const files = readdirSync(rulesDir).filter((file) => file.endsWith('.mdc'));
    const rules: Rule[] = [];

    for (const file of files) {
      const filePath = join(rulesDir, file);
      const content = readFileSync(filePath, 'utf-8');
      const rule = normalizeMDCToRule(file, content);
      rules.push(rule);
    }

    return rules;
  } catch (error) {
    console.error('Error reading docs rules:', error);
    return [];
  }
}
