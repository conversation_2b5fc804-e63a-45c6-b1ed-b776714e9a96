/**
 * Example usage and tests for MDC utilities
 */

import { generateMDCFromRule, generateMDCFromRules, sanitizeRuleFilename } from './mdc-utils';
import type { Rule } from './store';

// Example Rule object for testing
const exampleRule: Rule = {
  id: 'clg1234567890',
  title: 'React Component Best Practices',
  description: 'Guidelines for creating maintainable React components',
  content: `You are an expert React developer. Follow these best practices:

## Component Structure
- Use functional components with hooks
- Keep components small and focused
- Use TypeScript for type safety

## Naming Conventions
- Use PascalCase for component names
- Use camelCase for props and variables
- Use descriptive names that explain purpose

## Performance
- Use React.memo for expensive components
- Implement proper key props for lists
- Avoid inline object/function creation in render

## Example
\`\`\`tsx
interface ButtonProps {
  children: React.ReactNode;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

export const Button: React.FC<ButtonProps> = ({ 
  children, 
  onClick, 
  variant = 'primary' 
}) => {
  return (
    <button 
      className={\`btn btn-\${variant}\`}
      onClick={onClick}
    >
      {children}
    </button>
  );
};
\`\`\``,
  ideType: 'CURSOR',
  visibility: 'PUBLIC',
  applyType: 'manual',
  shareToken: 'abc123',
  createdAt: '2024-01-15T10:30:00.000Z',
  updatedAt: '2024-01-20T14:45:00.000Z',
  userId: 'user123',
  tags: [
    { tag: { id: 'tag1', name: 'react', color: '#61DAFB' } },
    { tag: { id: 'tag2', name: 'typescript', color: '#3178C6' } },
    { tag: { id: 'tag3', name: 'best-practices', color: '#10B981' } },
  ],
  user: {
    id: 'user123',
    name: 'John Doe',
    email: '<EMAIL>',
    image: null,
    avatar: null,
  },
};

// Example usage
console.log('=== MDC Generation Examples ===\n');

// 1. Generate MDC from a single rule
console.log('1. Single Rule MDC:');
const singleRuleMDC = generateMDCFromRule(exampleRule);
console.log(singleRuleMDC);
console.log(`\n${'='.repeat(50)}\n`);

// 2. Generate MDC from multiple rules
console.log('2. Multiple Rules MDC:');
const multipleRules = [
  exampleRule,
  {
    ...exampleRule,
    id: 'clg0987654321',
    title: 'CSS Grid Layout Guide',
    description: 'Master CSS Grid for modern layouts',
    content: 'Use CSS Grid for two-dimensional layouts...',
    tags: [
      { tag: { id: 'tag4', name: 'css', color: '#1572B6' } },
      { tag: { id: 'tag5', name: 'grid', color: '#FF6B6B' } },
    ],
  },
];

const multipleRulesMDC = generateMDCFromRules(multipleRules, {
  includeFileSeparators: true,
  includeRuleIndex: true,
  title: 'Frontend Development Rules',
  description: 'A collection of best practices for frontend development',
});
console.log(multipleRulesMDC);
console.log(`\n${'='.repeat(50)}\n`);

// 3. Filename sanitization
console.log('3. Filename Sanitization:');
const testTitles = [
  'React Component Best Practices',
  'CSS Grid & Flexbox: A Complete Guide!',
  'TypeScript/JavaScript Patterns (Advanced)',
  'Node.js API Development - Part 1',
];

testTitles.forEach((title) => {
  const sanitized = sanitizeRuleFilename(title);
  console.log(`"${title}" -> "${sanitized}"`);
});

// Export for use in other files
export { exampleRule, multipleRules };
