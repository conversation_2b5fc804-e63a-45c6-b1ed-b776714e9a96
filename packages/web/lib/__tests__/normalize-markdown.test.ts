import { normalizeMarkdownContent, RuleNormalizationError } from '../../../shared/lib/utils';

describe('normalizeMarkdownContent', () => {
  it('should normalize markdown content with frontmatter', async () => {
    const input = `---
title: "Test Rule"
description: "A test rule for validation"
applyType: "auto"
ideType: "CURSOR"
visibility: "PUBLIC"
tags: ["test", "example"]
---

# Test Rule

> A test rule for validation

## Instructions
- Follow these steps
- Be precise
- Test thoroughly

## Examples
\`\`\`typescript
const example = "code";
\`\`\`
`;

    const result = await normalizeMarkdownContent(input);

    expect(result.metadata.title).toBe('Test Rule');
    expect(result.metadata.description).toBe('A test rule for validation');
    expect(result.metadata.applyType).toBe('auto');
    expect(result.metadata.ideType).toBe('CURSOR');
    expect(result.metadata.visibility).toBe('PUBLIC');
    expect(result.metadata.tags).toEqual(['test', 'example']);
    expect(result.content).toContain('# Test Rule');
    expect(result.content).toContain('## Instructions');
    expect(result.rawContent).toBe(input);
  });

  it('should extract metadata from content when no frontmatter exists', async () => {
    const input = `# React Component Rule

> Create reusable React components

## Metadata

- **IDE Type:** CURSOR
- **Author:** John Doe
- **Created:** 8/3/2025
- **Updated:** 8/3/2025

## Instructions
- Use TypeScript
- Follow React best practices
`;

    const result = await normalizeMarkdownContent(input);

    expect(result.metadata.title).toBe('React Component Rule');
    expect(result.metadata.description).toBe('Create reusable React components');
    expect(result.metadata.ideType).toBe('CURSOR');
    expect(result.metadata.author).toBe('John Doe');
    expect(result.metadata.applyType).toBe('manual'); // default
  });

  it('should use defaults when metadata is missing', async () => {
    const input = `# Simple Rule

This is a basic rule without much metadata.
`;

    const result = await normalizeMarkdownContent(input, {
      defaultApplyType: 'always',
      defaultIdeType: 'GENERAL',
      defaultVisibility: 'PRIVATE',
    });

    expect(result.metadata.title).toBe('Simple Rule');
    expect(result.metadata.applyType).toBe('always');
    expect(result.metadata.ideType).toBe('GENERAL');
    expect(result.metadata.visibility).toBe('PRIVATE');
  });

  it('should throw error for empty input', async () => {
    await expect(normalizeMarkdownContent('')).rejects.toThrow(RuleNormalizationError);
    await expect(normalizeMarkdownContent('   ')).rejects.toThrow(RuleNormalizationError);
  });

  it('should throw error for missing title', async () => {
    const input = `---
description: "No title here"
---

Some content without a title.
`;

    await expect(normalizeMarkdownContent(input)).rejects.toThrow(RuleNormalizationError);
  });

  it('should skip validation when validateRequired is false', async () => {
    const input = `---
description: "No title here"
---

Some content without a title.
`;

    const result = await normalizeMarkdownContent(input, { validateRequired: false });
    expect(result.metadata.title).toBe('');
  });

  it('should parse arrays correctly', async () => {
    const input = `---
title: "Test Rule"
tags: ["react", "typescript", "hooks"]
emptyTags: []
---

# Test Rule
Content here.
`;

    const result = await normalizeMarkdownContent(input);
    expect(result.metadata.tags).toEqual(['react', 'typescript', 'hooks']);
  });

  it('should handle quoted values', async () => {
    const input = `---
title: 'Single Quoted Title'
description: "Double Quoted Description"
author: Unquoted Author
---

# Test Rule
Content here.
`;

    const result = await normalizeMarkdownContent(input);
    expect(result.metadata.title).toBe('Single Quoted Title');
    expect(result.metadata.description).toBe('Double Quoted Description');
    expect(result.metadata.author).toBe('Unquoted Author');
  });

  it('should extract title from first heading', async () => {
    const input = `# Main Title

## Subtitle

Content here.
`;

    const result = await normalizeMarkdownContent(input);
    expect(result.metadata.title).toBe('Main Title');
  });

  it('should extract description from blockquote after title', async () => {
    const input = `# Rule Title

> This is the description from blockquote

## Content
More content here.
`;

    const result = await normalizeMarkdownContent(input);
    expect(result.metadata.title).toBe('Rule Title');
    expect(result.metadata.description).toBe('This is the description from blockquote');
  });

  it('should work with Blob creation (as mentioned in requirements)', async () => {
    const input = `---
title: "API Documentation Rule"
description: "Guidelines for writing API documentation"
applyType: "auto"
---

# API Documentation Rule

> Guidelines for writing clear and comprehensive API documentation

## Requirements

1. Include request/response examples
2. Document all parameters
3. Specify error codes
4. Provide authentication details
`;

    const result = await normalizeMarkdownContent(input);

    // Create a Blob with the processed content (useful for downloads, etc.)
    const blob = new Blob([result.content], { type: 'text/markdown' });

    expect(blob.size).toBeGreaterThan(0);
    expect(blob.type).toBe('text/markdown');
    expect(result.metadata.title).toBe('API Documentation Rule');
    expect(result.metadata.applyType).toBe('auto');
  });
});
