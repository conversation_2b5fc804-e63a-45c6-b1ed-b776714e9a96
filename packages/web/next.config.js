/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  transpilePackages: ['@onlyrules/shared', '@onlyrules/docs', 'better-auth'],
  output: 'standalone',
  // Force App Router and disable Pages Router
  trailingSlash: false,

  // Prevent static generation of API routes and error pages
  async rewrites() {
    return [
      {
        source: '/sitemap.xml',
        destination: '/api/sitemap.xml',
      },
      {
        source: '/robots.txt',
        destination: '/api/robots.txt',
      },
    ];
  },
  // Disable static generation for error pages and API routes
  generateBuildId: async () => {
    return `build-${Date.now()}`;
  },
  // Disable static optimization for API routes
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, must-revalidate',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
