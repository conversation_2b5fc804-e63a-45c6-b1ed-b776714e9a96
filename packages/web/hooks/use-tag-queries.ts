import { useQuery } from '@tanstack/react-query';
import { queryKeys } from '@/lib/query-client';

// Types
interface Tag {
  id: string;
  name: string;
  color: string;
}

// API functions
const fetchTags = async (): Promise<Tag[]> => {
  const response = await fetch('/api/tags');

  if (!response.ok) {
    throw new Error('Failed to fetch tags');
  }

  return response.json();
};

// Query hooks
export const useTags = () => {
  return useQuery({
    queryKey: queryKeys.tags.lists(),
    queryFn: fetchTags,
    staleTime: 10 * 60 * 1000, // 10 minutes - tags don't change often
  });
};
