'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

// Types for ruleset data
export interface RulesetRule {
  order: number;
  rule: {
    id: string;
    title: string;
    description: string | null;
    content: string;
    visibility: 'PUBLIC' | 'PRIVATE';
    user: {
      id: string;
      name: string | null;
      email: string;
    };
    tags: Array<{
      tag: {
        id: string;
        name: string;
        color: string;
      };
    }>;
  };
}

export interface Ruleset {
  id: string;
  name: string;
  description: string | null;
  visibility: 'PUBLIC' | 'PRIVATE';
  shareToken: string | null;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string | null;
    email: string;
  };
  rules: RulesetRule[];
}

export interface CreateRulesetData {
  name: string;
  description?: string | null;
  visibility: 'PUBLIC' | 'PRIVATE';
  ruleIds: string[];
}

export interface UpdateRulesetData extends CreateRulesetData {
  id: string;
}

// Query keys
const QUERY_KEYS = {
  rulesets: ['rulesets'] as const,
  ruleset: (id: string) => ['rulesets', id] as const,
  userRulesets: (userId: string) => ['rulesets', 'user', userId] as const,
};

// Hook to fetch all rulesets with optional filters
export function useRulesets(filters?: { search?: string; visibility?: 'PUBLIC' | 'PRIVATE' }) {
  return useQuery({
    queryKey: [...QUERY_KEYS.rulesets, filters],
    queryFn: async (): Promise<Ruleset[]> => {
      const params = new URLSearchParams();
      if (filters?.search) params.set('search', filters.search);
      if (filters?.visibility) params.set('visibility', filters.visibility);

      const response = await fetch(`/api/rulesets?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch rulesets');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch a single ruleset by ID
export function useRuleset(id: string, shareToken?: string) {
  return useQuery({
    queryKey: QUERY_KEYS.ruleset(id),
    queryFn: async (): Promise<Ruleset> => {
      const params = new URLSearchParams();
      if (shareToken) params.set('token', shareToken);

      const response = await fetch(`/api/rulesets/${id}?${params}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Ruleset not found');
        }
        throw new Error('Failed to fetch ruleset');
      }
      return response.json();
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to create a new ruleset
export function useCreateRuleset() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateRulesetData): Promise<Ruleset> => {
      const response = await fetch('/api/rulesets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create ruleset');
      }

      return response.json();
    },
    onSuccess: (_newRuleset) => {
      // Invalidate and refetch rulesets
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.rulesets });
      toast.success('Ruleset created successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create ruleset');
    },
  });
}

// Hook to update an existing ruleset
export function useUpdateRuleset() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateRulesetData): Promise<Ruleset> => {
      const { id, ...updateData } = data;
      const response = await fetch(`/api/rulesets/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update ruleset');
      }

      return response.json();
    },
    onSuccess: (updatedRuleset) => {
      // Update the specific ruleset in cache
      queryClient.setQueryData(QUERY_KEYS.ruleset(updatedRuleset.id), updatedRuleset);
      // Invalidate rulesets list
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.rulesets });
      toast.success('Ruleset updated successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update ruleset');
    },
  });
}

// Hook to delete a ruleset
export function useDeleteRuleset() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`/api/rulesets/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete ruleset');
      }
    },
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: QUERY_KEYS.ruleset(deletedId) });
      // Invalidate rulesets list
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.rulesets });
      toast.success('Ruleset deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete ruleset');
    },
  });
}

// Hook to invalidate ruleset queries (useful for manual refresh)
export function useInvalidateRulesets() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({ queryKey: QUERY_KEYS.rulesets });
  };
}
