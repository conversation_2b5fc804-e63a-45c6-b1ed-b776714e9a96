import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { queryKeys } from '@/lib/query-client';
import type { Rule } from '@/lib/store';

// Types for API responses
interface RuleFilters {
  search?: string;
  tags?: string[];
  visibility?: string;
  page?: number;
  limit?: number;
}

interface RulesResponse {
  rules: Rule[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

interface ApiError {
  message: string;
  status?: number;
}

// API functions
const fetchRule = async (id: string): Promise<Rule> => {
  const response = await fetch(`/api/rules/${id}`);

  if (!response.ok) {
    const error: ApiError = {
      message:
        response.status === 404
          ? 'Rule not found'
          : response.status === 403
            ? "You don't have permission to view this rule"
            : 'Failed to load rule',
      status: response.status,
    };
    throw error;
  }

  return response.json();
};

const fetchRules = async (filters: RuleFilters = {}): Promise<RulesResponse> => {
  const params = new URLSearchParams();

  if (filters.search) params.set('search', filters.search);
  if (filters.tags?.length) params.set('tags', filters.tags.join(','));
  if (filters.visibility) params.set('visibility', filters.visibility);
  if (filters.page) params.set('page', filters.page.toString());
  if (filters.limit) params.set('limit', filters.limit.toString());

  const response = await fetch(`/api/rules?${params}`);

  if (!response.ok) {
    throw new Error('Failed to fetch rules');
  }

  const data = await response.json();

  // Transform response to match expected format
  return {
    rules: Array.isArray(data) ? data : data.rules || [],
    totalCount: data.totalCount || data.length || 0,
    totalPages: data.totalPages || 1,
    currentPage: data.currentPage || 1,
  };
};

const fetchRuleRaw = async (id: string): Promise<string> => {
  const response = await fetch(`/api/rules/raw?id=${id}`);

  if (!response.ok) {
    throw new Error('Failed to fetch raw rule content');
  }

  return response.text();
};

const updateRule = async ({ id, data }: { id: string; data: Partial<Rule> }): Promise<Rule> => {
  const response = await fetch(`/api/rules/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || 'Failed to update rule');
  }

  return response.json();
};

const createRule = async (data: Partial<Rule>): Promise<Rule> => {
  const response = await fetch('/api/rules', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || 'Failed to create rule');
  }

  return response.json();
};

const deleteRule = async (id: string): Promise<void> => {
  const response = await fetch(`/api/rules/${id}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || 'Failed to delete rule');
  }
};

// Query hooks
export const useRule = (id: string) => {
  return useQuery({
    queryKey: queryKeys.rules.detail(id),
    queryFn: () => fetchRule(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: Error & { status?: number }) => {
      // Don't retry on 404 or 403 errors
      if (error?.status === 404 || error?.status === 403) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

export const useRules = (filters: RuleFilters = {}) => {
  return useQuery({
    queryKey: queryKeys.rules.list(filters as Record<string, unknown>),
    queryFn: () => fetchRules(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes for lists
    placeholderData: (previousData) => previousData, // Keep previous data while fetching new
  });
};

export const useRuleRaw = (id: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.rules.raw(id),
    queryFn: () => fetchRuleRaw(id),
    enabled: !!id && enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes for raw content
  });
};

// Mutation hooks
export const useCreateRule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createRule,
    onMutate: async (newRule) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: queryKeys.rules.lists() });

      // Snapshot the previous value
      const previousRules = queryClient.getQueriesData({ queryKey: queryKeys.rules.lists() });

      // Optimistically update to the new value
      queryClient.setQueriesData({ queryKey: queryKeys.rules.lists() }, (old: any) => {
        if (!old) return old;

        const optimisticRule: Rule = {
          id: `temp-${Date.now()}`, // Temporary ID
          title: newRule.title || 'New Rule',
          description: newRule.description || null,
          content: newRule.content || '',
          visibility: newRule.visibility || 'PRIVATE',
          applyType: newRule.applyType || 'manual',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          userId: newRule.userId || '',
          tags: [],
          user: newRule.user,
          shareToken: null,
        };

        return {
          ...old,
          rules: [optimisticRule, ...(old.rules || [])],
          totalCount: (old.totalCount || 0) + 1,
        };
      });

      // Return a context object with the snapshotted value
      return { previousRules };
    },
    onError: (_err, _newRule, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousRules) {
        context.previousRules.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    onSuccess: (_newRule) => {
      // Invalidate rules lists to refetch them with the real data
      queryClient.invalidateQueries({
        queryKey: queryKeys.rules.lists(),
      });

      toast.success('Rule created successfully');
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.rules.lists() });
    },
  });
};

export const useUpdateRule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateRule,
    onMutate: async ({ id, data }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.rules.detail(id) });

      // Snapshot the previous value
      const previousRule = queryClient.getQueryData(queryKeys.rules.detail(id));

      // Optimistically update the rule detail
      queryClient.setQueryData(queryKeys.rules.detail(id), (old: Rule | undefined) => {
        if (!old) return old;
        return { ...old, ...data, updatedAt: new Date().toISOString() };
      });

      // Return a context with the previous data
      return { previousRule };
    },
    onError: (error: Error, { id }, context) => {
      // If the mutation fails, rollback
      if (context?.previousRule) {
        queryClient.setQueryData(queryKeys.rules.detail(id), context.previousRule);
      }
      toast.error(error.message || 'Failed to update rule');
    },
    onSuccess: (updatedRule) => {
      // Update the rule detail cache with real data
      queryClient.setQueryData(queryKeys.rules.detail(updatedRule.id), updatedRule);

      // Invalidate rules lists to refetch them
      queryClient.invalidateQueries({
        queryKey: queryKeys.rules.lists(),
      });

      toast.success('Rule updated successfully');
    },
  });
};

export const useDeleteRule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteRule,
    onMutate: async (ruleId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.rules.lists() });

      // Snapshot the previous value
      const previousRules = queryClient.getQueriesData({ queryKey: queryKeys.rules.lists() });

      // Optimistically remove the rule from all lists
      queryClient.setQueriesData({ queryKey: queryKeys.rules.lists() }, (old: any) => {
        if (!old) return old;

        return {
          ...old,
          rules: old.rules?.filter((rule: Rule) => rule.id !== ruleId) || [],
          totalCount: Math.max(0, (old.totalCount || 0) - 1),
        };
      });

      // Return a context object with the snapshotted value
      return { previousRules };
    },
    onError: (error: Error, _ruleId, context) => {
      // If the mutation fails, use the context to roll back
      if (context?.previousRules) {
        context.previousRules.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
      toast.error(error.message || 'Failed to delete rule');
    },
    onSuccess: (_, ruleId) => {
      // Remove the rule from detail cache
      queryClient.removeQueries({
        queryKey: queryKeys.rules.detail(ruleId),
      });

      toast.success('Rule deleted successfully');
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.rules.lists() });
    },
  });
};

// Utility hooks for common operations
export const usePrefetchRule = () => {
  const queryClient = useQueryClient();

  return (id: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.rules.detail(id),
      queryFn: () => fetchRule(id),
      staleTime: 5 * 60 * 1000,
    });
  };
};

export const useInvalidateRules = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.rules.all,
    });
  };
};
