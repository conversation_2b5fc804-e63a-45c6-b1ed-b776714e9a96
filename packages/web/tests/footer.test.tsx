import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { Footer } from '@/components/layout/footer';

// Mock Next.js components
jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: any) {
    return (
      <a href={href} {...props}>
        {children}
      </a>
    );
  };
});

describe('Footer Component', () => {
  it('renders the footer with correct structure', () => {
    render(<Footer />);

    // Check if footer element exists
    const footer = screen.getByRole('contentinfo');
    expect(footer).toBeInTheDocument();
  });

  it('displays company information', () => {
    render(<Footer />);

    // Check for company name
    expect(screen.getByText('OnlyRules')).toBeInTheDocument();

    // Check for company description
    expect(screen.getByText(/The ultimate AI prompt management platform/)).toBeInTheDocument();
  });

  it('includes all navigation sections', () => {
    render(<Footer />);

    // Check for section headings
    expect(screen.getByText('Product')).toBeInTheDocument();
    expect(screen.getByText('Resources')).toBeInTheDocument();
    expect(screen.getByText('Community')).toBeInTheDocument();
    expect(screen.getByText('Legal')).toBeInTheDocument();
  });

  it('includes important links', () => {
    render(<Footer />);

    // Check for key navigation links
    expect(screen.getByText('AI Prompt Templates')).toBeInTheDocument();
    expect(screen.getByText('Documentation')).toBeInTheDocument();
    expect(screen.getByText('GitHub Repository')).toBeInTheDocument();
    expect(screen.getByText('Privacy Policy')).toBeInTheDocument();
    expect(screen.getByText('Terms of Service')).toBeInTheDocument();
    expect(screen.getByText('Contact Us')).toBeInTheDocument();
  });

  it('includes contact information', () => {
    render(<Footer />);

    // Check for contact email
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();

    // Check for location
    expect(screen.getByText('Global Remote Team')).toBeInTheDocument();
  });

  it('includes social media links', () => {
    render(<Footer />);

    // Check for GitHub link
    const githubLinks = screen.getAllByText('GitHub');
    expect(githubLinks.length).toBeGreaterThan(0);
  });

  it('displays copyright information', () => {
    render(<Footer />);

    // Check for current year in copyright
    const currentYear = new Date().getFullYear();
    expect(
      screen.getByText(`© ${currentYear} OnlyRules. All rights reserved.`)
    ).toBeInTheDocument();
  });

  it('includes structured data for SEO', () => {
    render(<Footer />);

    // Check for JSON-LD script tag
    const scripts = document.querySelectorAll('script[type="application/ld+json"]');
    expect(scripts.length).toBeGreaterThan(0);

    // Verify structured data content
    const structuredData = JSON.parse(scripts[0].textContent || '{}');
    expect(structuredData['@type']).toBe('Organization');
    expect(structuredData.name).toBe('OnlyRules');
  });

  it('has proper accessibility attributes', () => {
    render(<Footer />);

    // Check for proper ARIA labels
    const footer = screen.getByRole('contentinfo');
    expect(footer).toHaveAttribute('aria-label', 'Site footer');
  });
});
