// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

import type { Metadata } from 'next';
import Link from 'next/link';
import { ONLEYRULES_ALL_TARGETS } from 'onlyrules';

export const metadata: Metadata = {
  title: 'Supported AI Code IDEs',
  description:
    'Explore OnlyRules integration guides for all supported AI-powered code IDEs. Learn how to enhance your coding workflow with AI assistance.',
  alternates: {
    canonical: '/ides',
  },
};

interface IDE {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  features: string[];
}

const supportedIDEs: IDE[] = [
  {
    id: ONLEYRULES_ALL_TARGETS.CURSOR,
    name: 'Cursor',
    description: 'The AI-first code editor built for pair programming with AI',
    icon: '🔷',
    color: 'bg-blue-500',
    features: ['AI-powered code completion', 'Natural language to code', 'Intelligent refactoring'],
  },
  {
    id: ONLEYRULES_ALL_TARGETS.AUGMENTCODE,
    name: 'Augment Code',
    description: 'Enhance your coding experience with advanced AI assistance',
    icon: '🟣',
    color: 'bg-purple-500',
    features: ['Smart code suggestions', 'Context-aware completions', 'Code optimization'],
  },
  {
    id: ONLEYRULES_ALL_TARGETS.WINDSURF,
    name: 'Windsurf',
    description: 'Ride the wave of AI-powered development with Windsurf IDE',
    icon: '🌊',
    color: 'bg-cyan-500',
    features: ['Flow-based coding', 'AI pair programming', 'Real-time collaboration'],
  },
  {
    id: ONLEYRULES_ALL_TARGETS.CLAUDE,
    name: 'Claude Dev',
    description: "Anthropic's Claude integrated into your development environment",
    icon: '🤖',
    color: 'bg-orange-500',
    features: ['Advanced reasoning', 'Long context windows', 'Ethical AI coding'],
  },
  {
    id: ONLEYRULES_ALL_TARGETS.COPILOT,
    name: 'GitHub Copilot',
    description: 'Your AI pair programmer powered by OpenAI Codex',
    icon: '🐙',
    color: 'bg-gray-700',
    features: ['Code suggestions', 'Multi-language support', 'Test generation'],
  },
  {
    id: ONLEYRULES_ALL_TARGETS.GEMINI,
    name: 'Gemini Code Assist',
    description: "Google's multimodal AI for enhanced code development",
    icon: '✨',
    color: 'bg-indigo-500',
    features: ['Multimodal understanding', 'Code explanation', 'Bug detection'],
  },
  {
    id: 'openai-codex',
    name: 'OpenAI Codex',
    description: 'The AI system that powers GitHub Copilot and more',
    icon: '🧠',
    color: 'bg-green-600',
    features: ['Natural language to code', 'Code translation', 'Documentation generation'],
  },
  {
    id: ONLEYRULES_ALL_TARGETS.CLINE,
    name: 'Cline',
    description: 'Autonomous AI assistant for complex coding tasks',
    icon: '🎯',
    color: 'bg-red-500',
    features: ['Autonomous coding', 'Multi-file editing', 'Project understanding'],
  },
  {
    id: ONLEYRULES_ALL_TARGETS.CODEBUDDY,
    name: 'Tencent CodeBuddy',
    description: 'Enterprise-grade AI coding assistant from Tencent Cloud',
    icon: '☁️',
    color: 'bg-blue-600',
    features: ['Enterprise features', 'Security focused', 'Cloud integration'],
  },
];

export default function IDEsPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mb-12 text-center">
        <h1 className="font-bold text-4xl">Supported AI Code IDEs</h1>
        <p className="mx-auto mt-4 max-w-3xl text-gray-600 text-xl">
          OnlyRules seamlessly integrates with the most popular AI-powered code editors.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {supportedIDEs.map((ide) => (
          <div key={ide.id} className="rounded-lg border p-6 transition-shadow hover:shadow-lg">
            <div className="mb-4">
              <span className="text-3xl">{ide.icon}</span>
            </div>
            <h3 className="mb-2 font-semibold text-xl">{ide.name}</h3>
            <p className="mb-4 text-gray-600">{ide.description}</p>

            <div className="mb-4 space-y-2">
              {ide.features.map((feature) => (
                <div key={feature} className="flex items-center gap-2 text-sm">
                  <div className="h-1.5 w-1.5 rounded-full bg-blue-500" />
                  <span className="text-gray-600">{feature}</span>
                </div>
              ))}
            </div>

            <Link
              href={`/ides/${ide.id}`}
              className="block w-full rounded bg-blue-500 py-2 text-center text-white hover:bg-blue-600"
            >
              View Integration Guide
            </Link>
          </div>
        ))}
      </div>

      <div className="mt-16 rounded-lg bg-gray-50 p-8 text-center">
        <h2 className="mb-4 font-semibold text-2xl">Don&apos;t see your IDE?</h2>
        <p className="mb-6 text-gray-600">
          We&apos;re constantly adding support for new AI-powered development tools.
        </p>
        <button
          type="button"
          className="rounded border border-gray-300 px-6 py-2 hover:bg-gray-100"
        >
          Request IDE Support
        </button>
      </div>
    </div>
  );
}
