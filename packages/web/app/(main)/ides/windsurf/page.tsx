import { Cloud, Waves, Wind } from 'lucide-react';
import type { Metadata } from 'next';
import IDEPageTemplate from '@/components/ide-page-template';

export const metadata: Metadata = {
  title: 'Windsurf IDE Integration - AI-Powered Flow Coding',
  description:
    'Complete guide to integrating OnlyRules with Windsurf IDE. Learn flow-based AI coding, setup instructions, and optimization tips for enhanced development.',
  keywords:
    'Windsurf IDE, AI flow coding, OnlyRules Windsurf, flow-based development, AI IDE integration, Windsurf setup, collaborative coding, AI pair programming',
  alternates: {
    canonical: '/ides/windsurf',
  },
  openGraph: {
    title: 'Windsurf IDE Integration - OnlyRules Guide',
    description:
      'Master flow-based AI coding with Windsurf IDE and OnlyRules. Step-by-step setup and best practices.',
    images: ['/images/windsurf-integration-og.png'],
  },
};

const windsurfData = {
  ide: {
    name: 'Windsurf',
    icon: '🌊',
    color: 'bg-cyan-500',
    description: 'Ride the wave of AI-powered development with flow-based coding',
    website: 'https://codeium.com/windsurf',
    version: '1.0+',
  },
  installation: {
    steps: [
      {
        title: 'Download Windsurf',
        description:
          "Get Windsurf from the official Codeium website. It's built on VS Code with advanced AI capabilities.",
        command: 'https://codeium.com/windsurf/download',
        note: 'Windsurf is available for all major operating systems and includes Codeium AI built-in.',
      },
      {
        title: 'Install and Launch',
        description:
          'Run the installer and launch Windsurf. The familiar VS Code interface makes it easy to get started.',
      },
      {
        title: 'Sign In to Codeium',
        description:
          'Create or sign in to your Codeium account to activate AI features. Free tier includes generous AI usage.',
        note: 'Windsurf comes with Codeium AI pre-integrated, offering advanced code completion and chat features.',
      },
      {
        title: 'Configure AI Settings',
        description:
          'Access the Windsurf AI settings to customize your coding experience and model preferences.',
        command: 'Cmd/Ctrl + Shift + P → Windsurf: AI Settings',
      },
    ],
  },
  integration: {
    steps: [
      {
        title: 'Browse OnlyRules Templates',
        description:
          'Visit OnlyRules and filter for Windsurf-compatible prompt rules and templates.',
        code: 'https://onlyrules.app/templates?ide=WINDSURF',
      },
      {
        title: 'Create Windsurf Rules',
        description:
          'Set up a .windsurf directory in your project with a rules.md file for custom prompts.',
        code: `mkdir .windsurf
touch .windsurf/rules.md`,
      },
      {
        title: 'Configure Flow Rules',
        description: 'Add your OnlyRules prompts to enable flow-based coding patterns in Windsurf.',
        code: `# .windsurf/rules.md

## Flow-Based Development Rules

### Component Flow
When creating new components:
1. Start with interface definitions
2. Implement core logic
3. Add error handling
4. Include comprehensive tests

### AI Collaboration Flow
- Use cascading prompts for complex features
- Break down tasks into atomic operations
- Maintain context between AI interactions

### Code Quality Standards
- Follow functional programming principles
- Implement proper error boundaries
- Use TypeScript strictly
- Document complex logic flows`,
      },
      {
        title: 'Activate Flow Mode',
        description:
          "Use Windsurf's unique flow mode to chain AI operations and build features incrementally.",
        code: 'Ctrl/Cmd + Shift + F → Start Flow Mode',
      },
    ],
  },
  features: [
    {
      title: 'Flow-Based Coding',
      description: 'Chain AI operations together for complex multi-step development tasks',
      icon: 'waves',
    },
    {
      title: 'Multi-Agent Collaboration',
      description: 'Multiple AI agents working together on different aspects of your code',
      icon: 'wind',
    },
    {
      title: 'Cloud Sync',
      description: 'Sync your flows and rules across devices with cloud integration',
      icon: 'cloud',
    },
  ],
  examples: [
    {
      title: 'Multi-File Feature Implementation',
      description: 'Build a complete feature across multiple files using flow mode',
      prompt: `Flow: Create user authentication system
Step 1: Design database schema for users
Step 2: Create API endpoints for login/register
Step 3: Build React components for auth forms
Step 4: Implement JWT token handling
Step 5: Add middleware for protected routes
Step 6: Create unit and integration tests`,
      result: 'Complete authentication system with all components properly integrated',
    },
    {
      title: 'Cascading Refactor',
      description: 'Refactor code across your entire codebase with intelligent flow',
      prompt: `Flow: Migrate from Redux to Zustand
- Analyze current Redux implementation
- Create equivalent Zustand stores
- Update all component connections
- Migrate middleware and side effects
- Update tests to match new state management
- Remove Redux dependencies`,
      result: 'Seamless migration with all components updated and tested',
    },
    {
      title: 'AI-Driven Code Review',
      description: 'Perform comprehensive code review with actionable improvements',
      prompt: `Flow: Review and improve codebase
1. Analyze code for performance bottlenecks
2. Identify security vulnerabilities
3. Suggest architectural improvements
4. Implement critical fixes
5. Add missing documentation
6. Optimize bundle size`,
      result: 'Detailed review with implemented improvements and documentation',
    },
  ],
  tips: [
    "Use Windsurf's flow mode to break complex tasks into manageable AI-assisted steps",
    'Leverage the multi-agent system by assigning different aspects of a feature to different agents',
    'Create reusable flow templates for common development patterns in your team',
    'Use the cascade feature to propagate changes intelligently across related files',
    'Enable Windsurf\'s "Context Preservation" to maintain AI understanding across sessions',
    'Combine OnlyRules templates with Windsurf flows for maximum productivity',
    'Use the built-in collaboration features to share flows with team members',
    'Set up project-specific flows that align with your architecture patterns',
    "Take advantage of Windsurf's VS Code heritage by using familiar extensions",
    'Regularly sync your flows to the cloud for backup and cross-device access',
  ],
};

export default function WindsurfPage() {
  // Convert string identifiers to JSX icons
  const iconMap = {
    waves: <Waves className="h-5 w-5" />,
    wind: <Wind className="h-5 w-5" />,
    cloud: <Cloud className="h-5 w-5" />,
  };

  const featuresWithIcons = windsurfData.features.map((feature) => ({
    ...feature,
    icon: iconMap[feature.icon as keyof typeof iconMap],
  }));

  return <IDEPageTemplate {...windsurfData} features={featuresWithIcons} />;
}
