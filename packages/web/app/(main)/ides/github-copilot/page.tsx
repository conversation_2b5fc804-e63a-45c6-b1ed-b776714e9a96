import { Brain, Code, Shield } from 'lucide-react';
import type { Metadata } from 'next';
import IDEPageTemplate from '@/components/ide-page-template';

export const metadata: Metadata = {
  title: 'GitHub Copilot Integration - AI Pair Programming Guide',
  description:
    'Master GitHub Copilot with OnlyRules integration. Learn setup, configuration, and advanced techniques for AI-powered code completion and generation.',
  keywords:
    'GitHub Copilot, AI pair programming, OnlyRules GitHub Copilot, VS Code AI, code completion, AI coding assistant, Copilot setup, OpenAI Codex',
  alternates: {
    canonical: '/ides/github-copilot',
  },
  openGraph: {
    title: 'GitHub Copilot Integration Guide - OnlyRules',
    description: 'Enhance GitHub Copilot with custom prompts and best practices from OnlyRules.',
    images: ['/images/github-copilot-integration-og.png'],
  },
};

const githubCopilotData = {
  ide: {
    name: 'GitHub Copilot',
    icon: '🐙',
    color: 'bg-gray-700',
    description: 'Your AI pair programmer powered by OpenAI Codex',
    website: 'https://github.com/features/copilot',
    version: '1.150+',
  },
  installation: {
    steps: [
      {
        title: 'Install VS Code or Supported IDE',
        description:
          'GitHub Copilot works with VS Code, Visual Studio, Neovim, and JetBrains IDEs.',
        command: 'https://code.visualstudio.com/download',
        note: "We'll use VS Code for this guide, but the process is similar for other IDEs.",
      },
      {
        title: 'Install GitHub Copilot Extension',
        description: 'Open the Extensions view in VS Code and search for "GitHub Copilot".',
        command: 'ext install GitHub.copilot',
      },
      {
        title: 'Sign In to GitHub',
        description: 'Authenticate with your GitHub account that has Copilot access.',
        note: 'GitHub Copilot requires a subscription ($10/month for individuals, free for students/teachers).',
      },
      {
        title: 'Configure Copilot Settings',
        description: 'Customize Copilot behavior in VS Code settings for optimal performance.',
        command: 'Cmd/Ctrl + , → Extensions → GitHub Copilot',
      },
    ],
  },
  integration: {
    steps: [
      {
        title: 'Access OnlyRules Templates',
        description: 'Find GitHub Copilot-optimized prompt patterns on OnlyRules.',
        code: 'https://onlyrules.app/templates?ide=GITHUB_COPILOT',
      },
      {
        title: 'Create Project Configuration',
        description: 'Set up a .github/copilot-rules.md file for project-specific prompts.',
        code: `mkdir -p .github
touch .github/copilot-rules.md`,
      },
      {
        title: 'Define Coding Patterns',
        description: "Add OnlyRules templates to guide Copilot's suggestions in your project.",
        code: `# .github/copilot-rules.md

## Project Conventions

### TypeScript Guidelines
- Always use strict TypeScript
- Define interfaces for all data structures
- Prefer type over interface for unions
- Use proper generic constraints

### React Patterns
- Functional components only
- Custom hooks for logic extraction
- Props destructuring in parameters
- Proper error boundaries

### Testing Standards
- Test-first development
- 80% minimum coverage
- Integration tests for APIs
- E2E tests for critical paths

### Documentation
- JSDoc for public APIs
- README for each module
- Inline comments for complex logic`,
      },
      {
        title: 'Leverage Comment-Driven Development',
        description: "Use detailed comments to guide Copilot's code generation.",
        code: `// TODO: Create a custom hook that:
// 1. Fetches user data from API
// 2. Caches results in localStorage
// 3. Implements exponential backoff retry
// 4. Returns loading, error, and data states
// 5. Cleans up on unmount`,
      },
    ],
  },
  features: [
    {
      title: 'Context-Aware Suggestions',
      description: 'Analyzes surrounding code to provide relevant completions',
      icon: 'brain',
    },
    {
      title: 'Multi-Language Support',
      description: 'Works with dozens of programming languages and frameworks',
      icon: 'code',
    },
    {
      title: 'Security Filtering',
      description: 'Filters out insecure code patterns and secrets',
      icon: 'shield',
    },
  ],
  examples: [
    {
      title: 'Function Implementation from Comments',
      description: 'Generate complete functions from detailed comments',
      prompt: `// Function to validate email with the following rules:
// - Must contain @ symbol
// - Domain must have at least one dot
// - No special characters except . - _ @
// - Length between 5 and 254 characters
// - Return object with isValid and error message
function validateEmail(email: string): ValidationResult {`,
      result: 'Complete email validation function with all specified rules implemented',
    },
    {
      title: 'Test Suite Generation',
      description: 'Create comprehensive tests from function signatures',
      prompt: `describe('UserService', () => {
  // Test all methods of UserService class
  // Include happy path and edge cases
  // Mock external dependencies
  // Test error handling
  // Verify proper cleanup`,
      result: 'Full test suite with mocks, edge cases, and proper assertions',
    },
    {
      title: 'API Client Generation',
      description: 'Build typed API clients from endpoint descriptions',
      prompt: `// Create a typed API client for the following endpoints:
// GET /api/users - returns User[]
// GET /api/users/:id - returns User
// POST /api/users - accepts CreateUserDto, returns User
// PUT /api/users/:id - accepts UpdateUserDto, returns User
// DELETE /api/users/:id - returns void
// Include proper error handling and TypeScript types
class UserApiClient {`,
      result: 'Fully typed API client with all CRUD operations and error handling',
    },
  ],
  tips: [
    "Write detailed comments before implementing functions to guide Copilot's suggestions",
    'Use meaningful variable and function names to improve suggestion quality',
    'Accept suggestions with Tab, reject with Esc, or cycle through alternatives with Alt+]',
    'Disable Copilot in sensitive files by adding them to .copilotignore',
    'Use Copilot Chat (Ctrl+I) for more complex code generation tasks',
    'Create code snippets and patterns that Copilot can learn from in your project',
    'Review all Copilot suggestions carefully, especially for security-critical code',
    'Combine Copilot with OnlyRules templates for consistent code patterns',
    'Use ghost text preview to see suggestions before accepting them',
    'Enable "Show Multiple Solutions" to explore different implementation approaches',
  ],
};

export default function GitHubCopilotPage() {
  // Convert string identifiers to JSX icons
  const iconMap = {
    brain: <Brain className="h-5 w-5" />,
    code: <Code className="h-5 w-5" />,
    shield: <Shield className="h-5 w-5" />,
  };

  const featuresWithIcons = githubCopilotData.features.map((feature) => ({
    ...feature,
    icon: iconMap[feature.icon as keyof typeof iconMap],
  }));

  return <IDEPageTemplate {...githubCopilotData} features={featuresWithIcons} />;
}
