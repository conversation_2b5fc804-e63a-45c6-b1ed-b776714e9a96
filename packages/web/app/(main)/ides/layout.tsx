import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: {
    template: '%s | OnlyRules - AI Code IDE Integration',
    default: 'Supported IDEs | OnlyRules',
  },
  description:
    'Learn how to integrate OnlyRules with your favorite AI-powered code IDE. Step-by-step tutorials for Cursor, VS Code, WebStorm, and more.',
  keywords:
    'AI IDE integration, OnlyRules tutorial, Cursor AI, VS Code AI, WebStorm AI, IDE setup, AI coding assistant, prompt rules, code generation',
  openGraph: {
    title: 'Supported IDEs - OnlyRules Integration',
    description: 'Complete guides for integrating OnlyRules with popular AI-powered IDEs',
    type: 'website',
  },
};

export default function IDEsLayout({ children }: { children: React.ReactNode }) {
  return <div className="min-h-screen">{children}</div>;
}
