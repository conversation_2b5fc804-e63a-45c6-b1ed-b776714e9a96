import { <PERSON>, Button, Container, Flex, Heading, Text, TextField } from '@radix-ui/themes';
import { Github, Mail, MapPin } from 'lucide-react';
import type { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Contact Us - OnlyRules',
  description:
    "Get in touch with the OnlyRules team. We're here to help with your AI prompt management needs.",
  robots: 'index, follow',
};

export default function ContactPage() {
  return (
    <Container size="3" className="py-12">
      <div className="mx-auto max-w-4xl">
        <div className="mb-12 text-center">
          <Heading size="8" className="mb-4">
            Get in Touch
          </Heading>
          <Text size="4" color="gray" className="mx-auto max-w-2xl">
            Have questions about OnlyRules? Need help with AI prompt management? We'd love to hear
            from you and help you boost your coding productivity.
          </Text>
        </div>

        <Flex direction={{ initial: 'column', md: 'row' }} gap="8" className="mb-12">
          {/* Contact Information */}
          <Box className="flex-1">
            <Heading size="5" className="mb-6">
              Contact Information
            </Heading>

            <Flex direction="column" gap="6">
              <Flex align="center" gap="4">
                <Box className="rounded-lg bg-[var(--accent-3)] p-3">
                  <Mail size={20} className="text-[var(--accent-9)]" />
                </Box>
                <Box>
                  <Text size="3" weight="medium" className="mb-1 block">
                    Email Support
                  </Text>
                  <Link
                    href="mailto:<EMAIL>"
                    className="text-[var(--accent-9)] hover:underline"
                  >
                    <EMAIL>
                  </Link>
                </Box>
              </Flex>

              <Flex align="center" gap="4">
                <Box className="rounded-lg bg-[var(--accent-3)] p-3">
                  <Github size={20} className="text-[var(--accent-9)]" />
                </Box>
                <Box>
                  <Text size="3" weight="medium" className="mb-1 block">
                    GitHub Repository
                  </Text>
                  <Link
                    href="https://github.com/ranglang/onlyrules"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[var(--accent-9)] hover:underline"
                  >
                    github.com/ranglang/onlyrules
                  </Link>
                </Box>
              </Flex>

              <Flex align="center" gap="4">
                <Box className="rounded-lg bg-[var(--accent-3)] p-3">
                  <MapPin size={20} className="text-[var(--accent-9)]" />
                </Box>
                <Box>
                  <Text size="3" weight="medium" className="mb-1 block">
                    Location
                  </Text>
                  <Text size="2" color="gray">
                    Global Remote Team
                  </Text>
                </Box>
              </Flex>
            </Flex>
          </Box>

          {/* Quick Contact Form */}
          <Box className="flex-1">
            <Heading size="5" className="mb-6">
              Quick Contact
            </Heading>

            <form className="space-y-4">
              <Box>
                <Text size="2" weight="medium" className="mb-2 block">
                  Name
                </Text>
                <TextField.Root placeholder="Your name" required />
              </Box>

              <Box>
                <Text size="2" weight="medium" className="mb-2 block">
                  Email
                </Text>
                <TextField.Root type="email" placeholder="<EMAIL>" required />
              </Box>

              <Box>
                <Text size="2" weight="medium" className="mb-2 block">
                  Subject
                </Text>
                <TextField.Root placeholder="How can we help you?" required />
              </Box>

              <Box>
                <Text size="2" weight="medium" className="mb-2 block">
                  Message
                </Text>
                <textarea
                  className="resize-vertical min-h-[120px] w-full rounded-md border border-[var(--gray-6)] bg-[var(--color-surface)] p-3 text-[var(--gray-12)] placeholder:text-[var(--gray-9)] focus:border-transparent focus:outline-none focus:ring-2 focus:ring-[var(--accent-8)]"
                  placeholder="Tell us more about your question or how we can help..."
                  required
                />
              </Box>

              <Button size="3" className="w-full">
                Send Message
              </Button>
            </form>

            <Text size="1" color="gray" className="mt-4 block">
              We typically respond within 24 hours during business days.
            </Text>
          </Box>
        </Flex>

        {/* FAQ Section */}
        <Box className="border-[var(--gray-6)] border-t pt-12">
          <Heading size="5" className="mb-6 text-center">
            Frequently Asked Questions
          </Heading>

          <Flex direction="column" gap="6" className="mx-auto max-w-3xl">
            <Box>
              <Text size="3" weight="medium" className="mb-2 block">
                How do I get started with OnlyRules?
              </Text>
              <Text size="2" color="gray">
                Check out our{' '}
                <Link
                  href="/docs/getting-started"
                  className="text-[var(--accent-9)] hover:underline"
                >
                  getting started guide
                </Link>{' '}
                or explore our{' '}
                <Link href="/templates" className="text-[var(--accent-9)] hover:underline">
                  template library
                </Link>{' '}
                to find AI prompts for your favorite IDE.
              </Text>
            </Box>

            <Box>
              <Text size="3" weight="medium" className="mb-2 block">
                Which IDEs are supported?
              </Text>
              <Text size="2" color="gray">
                We support popular IDEs including Cursor, Windsurf, GitHub Copilot, Claude, and many
                more. Visit our{' '}
                <Link href="/ides" className="text-[var(--accent-9)] hover:underline">
                  IDE integrations page
                </Link>{' '}
                for the full list.
              </Text>
            </Box>

            <Box>
              <Text size="3" weight="medium" className="mb-2 block">
                Is OnlyRules free to use?
              </Text>
              <Text size="2" color="gray">
                Yes! OnlyRules is open source and free to use. You can create, share, and use AI
                prompt rules without any cost.
              </Text>
            </Box>

            <Box>
              <Text size="3" weight="medium" className="mb-2 block">
                How can I contribute to the project?
              </Text>
              <Text size="2" color="gray">
                We welcome contributions! Visit our{' '}
                <Link
                  href="https://github.com/ranglang/onlyrules"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-[var(--accent-9)] hover:underline"
                >
                  GitHub repository
                </Link>{' '}
                to report issues, suggest features, or submit pull requests.
              </Text>
            </Box>
          </Flex>
        </Box>
      </div>
    </Container>
  );
}
