import '../globals.css';
import type { <PERSON><PERSON><PERSON>, Viewport } from 'next';
import { Footer } from '@/components/layout/footer';
import { NavbarWrapper } from '@/components/layout/navbar-wrapper';
import { JotaiProvider } from '@/components/providers/jotai-provider';
import { QueryProvider } from '@/components/providers/query-provider';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { ToasterProvider } from '@/components/providers/toaster-provider';

// Remove force-dynamic to allow individual pages to choose their rendering strategy
// export const dynamic = 'force-dynamic';
// export const revalidate = 0;
export const runtime = 'nodejs';

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  title: 'OnlyRules - AI Prompt Management Platform',
  description:
    'Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.',
  keywords:
    'AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, <PERSON>ie, <PERSON>, <PERSON>ma, <PERSON>, Tencent Cloud CodeBuddy',
  authors: [{ name: 'OnlyRules Team' }],
  publisher: 'OnlyRules',
  category: 'Technology',
  classification: 'Software Development Tools',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'OnlyRules - AI Prompt Management Platform',
    description: 'Create, organize, and share AI prompt rules for your favorite IDEs.',
    type: 'website',
    locale: 'en_US',
    siteName: 'OnlyRules',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'OnlyRules - AI Prompt Management Platform',
    description: 'Create, organize, and share AI prompt rules for your favorite IDEs.',
  },
  alternates: {
    canonical: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'hsl(0 0% 100%)' },
    { media: '(prefers-color-scheme: dark)', color: 'hsl(240 10% 3.9%)' },
  ],
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className="radix-themes light">
      <body suppressHydrationWarning className="flex min-h-screen flex-col">
        <ThemeProvider>
          <JotaiProvider>
            <QueryProvider>
              <NavbarWrapper />
              <main className="flex-1">{children}</main>
              <Footer />
              <ToasterProvider />
            </QueryProvider>
          </JotaiProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
