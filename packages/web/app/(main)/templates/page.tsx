'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Container,
  Flex,
  Select,
  Tabs,
  TextField,
} from '@radix-ui/themes';
import { BookOpen, Code, Filter, Package, Search, Star, Users, Zap } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { RulesetCard } from '@/components/ruleset-card';
import type { Ruleset, RulesetRule } from '@/hooks/use-ruleset-queries';
import { useRulesets } from '@/hooks/use-ruleset-queries';
import { useTags } from '@/hooks/use-tag-queries';

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

export default function RulesetPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedIDE, setSelectedIDE] = useState('ALL');

  // Use React Query for data fetching
  const {
    data: rulesets = [],
    isLoading: loading,
    error: rulesetsError,
  } = useRulesets({
    search: searchQuery || undefined,
    visibility: 'PUBLIC',
  });

  const { data: tags = [], error: tagsError } = useTags();

  // Handle errors
  useEffect(() => {
    if (rulesetsError) {
      console.error('Error fetching rulesets:', rulesetsError);
      toast.error('Failed to fetch rulesets');
    }
    if (tagsError) {
      console.error('Error fetching tags:', tagsError);
      toast.error('Failed to fetch tags');
    }
  }, [rulesetsError, tagsError]);

  // Client-side filtering for tags and IDE since API doesn't support these filters
  const filteredRulesets = useMemo(() => {
    let filtered = rulesets;

    // Filter by tags
    if (selectedTags.length > 0) {
      filtered = filtered.filter((ruleset: Ruleset) =>
        ruleset.rules.some((rulesetRule: RulesetRule) =>
          rulesetRule.rule.tags.some(
            (ruleTag: { tag: { id: string; name: string; color: string } }) =>
              selectedTags.includes(ruleTag.tag.name)
          )
        )
      );
    }

    return filtered;
  }, [rulesets, selectedTags]);

  const toggleTag = (tagName: string) => {
    setSelectedTags((prev) =>
      prev.includes(tagName) ? prev.filter((t) => t !== tagName) : [...prev, tagName]
    );
  };

  const featuredRulesets = Array.isArray(filteredRulesets) ? filteredRulesets.slice(0, 6) : [];
  const allRulesets = Array.isArray(filteredRulesets) ? filteredRulesets : [];

  const categories = [
    {
      name: 'Code Generation',
      description: 'Ruleset collections for generating code snippets and functions',
      icon: Code,
      count: allRulesets.filter((ruleset: Ruleset) =>
        ruleset.rules.some((rulesetRule: RulesetRule) =>
          rulesetRule.rule.tags.some((t: { tag: { id: string; name: string; color: string } }) =>
            t.tag.name.toLowerCase().includes('generation')
          )
        )
      ).length,
    },
    {
      name: 'Code Review',
      description: 'Ruleset collections for automated code review and analysis',
      icon: BookOpen,
      count: allRulesets.filter((ruleset: Ruleset) =>
        ruleset.rules.some((rulesetRule: RulesetRule) =>
          rulesetRule.rule.tags.some((t: { tag: { id: string; name: string; color: string } }) =>
            t.tag.name.toLowerCase().includes('review')
          )
        )
      ).length,
    },
    {
      name: 'Optimization',
      description: 'Ruleset collections for code optimization and performance',
      icon: Zap,
      count: allRulesets.filter((ruleset: Ruleset) =>
        ruleset.rules.some((rulesetRule: RulesetRule) =>
          rulesetRule.rule.tags.some((t: { tag: { id: string; name: string; color: string } }) =>
            t.tag.name.toLowerCase().includes('optimization')
          )
        )
      ).length,
    },
    {
      name: 'Documentation',
      description: 'Ruleset collections for generating documentation',
      icon: Users,
      count: allRulesets.filter((ruleset: Ruleset) =>
        ruleset.rules.some((rulesetRule: RulesetRule) =>
          rulesetRule.rule.tags.some((t: { tag: { id: string; name: string; color: string } }) =>
            t.tag.name.toLowerCase().includes('documentation')
          )
        )
      ).length,
    },
  ];

  return (
    <Container size="4" py="8">
      <Flex direction="column" gap="8">
        {/* Header */}
        <Box className="text-center">
          <Flex direction="column" gap="4">
            <Flex align="center" justify="center" gap="2" mb="4">
              <Package className="h-8 w-8 text-primary" />
              <h1 className="font-bold text-4xl">Ruleset Library</h1>
            </Flex>
            <p className="mx-auto max-w-2xl text-muted-foreground text-xl">
              Discover and download community-created ruleset collections to boost your coding
              productivity
            </p>
          </Flex>
        </Box>

        {/* Categories */}
        <Box>
          <Flex direction="column" gap="6">
            <h2 className="font-bold text-2xl">Browse by Category</h2>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {categories.map((category) => (
                <Card
                  key={category.name}
                  className="cursor-pointer transition-shadow hover:shadow-lg"
                >
                  <Box>
                    <Flex align="center" gap="3">
                      <Flex
                        align="center"
                        justify="center"
                        className="h-10 w-10 rounded-lg bg-primary/10"
                      >
                        <category.icon className="h-5 w-5 text-primary" />
                      </Flex>
                      <Box>
                        <div className="font-semibold text-lg">{category.name}</div>
                        <Badge variant="soft">{category.count} rulesets</Badge>
                      </Box>
                    </Flex>
                  </Box>
                  <Box>
                    <p className="text-muted-foreground text-sm">{category.description}</p>
                  </Box>
                </Card>
              ))}
            </div>
          </Flex>
        </Box>

        {/* Search and Filters */}
        <Flex direction={{ initial: 'column', md: 'row' }} gap="4">
          <Box className="flex-1">
            <TextField.Root
              placeholder="Search rulesets..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            >
              <TextField.Slot>
                <Search className="h-4 w-4 text-muted-foreground" />
              </TextField.Slot>
            </TextField.Root>
          </Box>

          <Select.Root value={selectedIDE} onValueChange={setSelectedIDE}>
            <Select.Trigger className="w-full md:w-48" placeholder="IDE Type" />
            <Select.Content>
              <Select.Item value="ALL">All IDEs</Select.Item>
              <Select.Item value="GENERAL">General</Select.Item>
              <Select.Item value="CURSOR">Cursor</Select.Item>
              <Select.Item value="AUGMENT">Augment Code</Select.Item>
              <Select.Item value="WINDSURF">Windsurf</Select.Item>
              <Select.Item value="CLAUDE">Claude</Select.Item>
              <Select.Item value="GITHUB_COPILOT">GitHub Copilot</Select.Item>
              <Select.Item value="GEMINI">Gemini</Select.Item>
              <Select.Item value="OPENAI_CODEX">OpenAI Codex</Select.Item>
              <Select.Item value="CLINE">Cline</Select.Item>
              <Select.Item value="JUNIE">Junie</Select.Item>
              <Select.Item value="TRAE">Trae</Select.Item>
              <Select.Item value="LINGMA">Lingma</Select.Item>
              <Select.Item value="KIRO">Kiro</Select.Item>
              <Select.Item value="TENCENT_CODEBUDDY">Tencent Cloud CodeBuddy</Select.Item>
            </Select.Content>
          </Select.Root>
        </Flex>

        {/* Tag Filters */}
        {tags.length > 0 && (
          <Box>
            <Flex direction="column" gap="2">
              <Flex align="center" gap="2">
                <Filter className="h-4 w-4" />
                <span className="font-medium text-sm">Filter by tags:</span>
              </Flex>
              <Flex wrap="wrap" gap="2">
                {tags.map((tag) => (
                  <Badge
                    key={tag.id}
                    variant={selectedTags.includes(tag.name) ? 'solid' : 'outline'}
                    className="cursor-pointer"
                    onClick={() => toggleTag(tag.name)}
                    style={{
                      borderColor: tag.color,
                      backgroundColor: selectedTags.includes(tag.name) ? tag.color : 'transparent',
                    }}
                  >
                    {tag.name}
                  </Badge>
                ))}
              </Flex>
            </Flex>
          </Box>
        )}

        {/* Content */}
        <Tabs.Root defaultValue="featured" className="space-y-6">
          <Tabs.List>
            <Tabs.Trigger value="featured">Featured</Tabs.Trigger>
            <Tabs.Trigger value="all">All Rulesets ({allRulesets.length})</Tabs.Trigger>
          </Tabs.List>

          <Tabs.Content value="featured" className="space-y-6">
            <div className="space-y-4">
              <h3 className="font-semibold text-xl">Featured Rulesets</h3>
              {featuredRulesets.length === 0 ? (
                <Card className="py-12 text-center">
                  <Star className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                  <h3 className="mb-2 font-semibold text-lg">No featured rulesets yet</h3>
                  <p className="text-muted-foreground">
                    Check back later for curated ruleset collections from the community
                  </p>
                </Card>
              ) : (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {featuredRulesets.map((ruleset) => (
                    <RulesetCard key={ruleset.id} ruleset={ruleset} />
                  ))}
                </div>
              )}
            </div>
          </Tabs.Content>

          <Tabs.Content value="all" className="space-y-6">
            {loading ? (
              <div className="py-12 text-center">
                <div className="mx-auto h-8 w-8 animate-spin rounded-full border-primary border-b-2"></div>
                <p className="mt-4 text-muted-foreground">Loading rulesets...</p>
              </div>
            ) : allRulesets.length === 0 ? (
              <Card className="py-12 text-center">
                <Package className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                <h3 className="mb-2 font-semibold text-lg">No rulesets found</h3>
                <p className="mb-4 text-muted-foreground">
                  Try adjusting your search criteria or check back later
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedTags([]);
                    setSelectedIDE('ALL');
                  }}
                >
                  Clear Filters
                </Button>
              </Card>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {allRulesets.map((ruleset) => (
                  <RulesetCard key={ruleset.id} ruleset={ruleset} />
                ))}
              </div>
            )}
          </Tabs.Content>
        </Tabs.Root>
      </Flex>
    </Container>
  );
}
