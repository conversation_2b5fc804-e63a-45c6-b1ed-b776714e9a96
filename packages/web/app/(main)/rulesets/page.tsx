import { Container } from '@radix-ui/themes';
import { RulesetsClient } from './rulesets-client';
import type { Metadata } from 'next';

// Enable static generation at build time
export async function generateStaticParams() {
  // Return an empty array since this page doesn't have dynamic segments
  return [];
}

// Remove force-dynamic to enable static generation
// The page shell will be statically generated, while the client component handles dynamic data

export const metadata: Metadata = {
  title: 'Rulesets - OnlyRules',
  description: 'Create and manage collections of AI rules for quick deployment. Organize your AI prompt rules into reusable rulesets.',
  keywords: 'AI rulesets, prompt collections, rule management, AI templates, prompt organization',
  openGraph: {
    title: 'Rulesets - OnlyRules',
    description: 'Create and manage collections of AI rules for quick deployment.',
    type: 'website',
    url: '/rulesets',
    siteName: 'OnlyRules',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Rulesets - OnlyRules',
    description: 'Create and manage collections of AI rules for quick deployment.',
  },
  alternates: {
    canonical: '/rulesets',
  },
};

export default function RulesetsPage() {
  return (
    <Container size="4" py="8">
      <RulesetsClient />
    </Container>
  );
}
