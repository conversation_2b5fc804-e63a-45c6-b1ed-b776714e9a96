'use client';

import {
  <PERSON>,
  But<PERSON>,
  Card,
  Dialog,
  Flex,
  Heading,
  Select,
  Tabs,
  Text,
  TextField,
} from '@radix-ui/themes';
import { Eye, Lock, Package, Plus, Search, Users } from 'lucide-react';
import { useMemo, useState } from 'react';
import { RulesetCard } from '@/components/ruleset-card';
import { RulesetForm } from '@/components/ruleset-form';
import {
  type CreateRulesetData,
  type Ruleset,
  type UpdateRulesetData,
  useCreateRuleset,
  useDeleteRuleset,
  useRulesets,
  useUpdateRuleset,
} from '@/hooks/use-ruleset-queries';
import { useSession } from '@/lib/auth-client';

export function RulesetsClient() {
  const { data: session } = useSession();
  const [searchQuery, setSearchQuery] = useState('');
  const [visibilityFilter, setVisibilityFilter] = useState<'ALL' | 'PUBLIC' | 'PRIVATE'>('ALL');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingRuleset, setEditingRuleset] = useState<Ruleset | null>(null);

  // Memoize the filters object to prevent unnecessary re-renders
  const rulesetFilters = useMemo(
    () => ({
      search: searchQuery || undefined,
      visibility: visibilityFilter !== 'ALL' ? visibilityFilter : undefined,
    }),
    [searchQuery, visibilityFilter]
  );

  // Use query hooks for data fetching
  const {
    data: rulesets = [],
    isLoading: rulesetsLoading,
    error: _rulesetsError,
  } = useRulesets(rulesetFilters);

  // Mutation hooks
  const createRulesetMutation = useCreateRuleset();
  const updateRulesetMutation = useUpdateRuleset();
  const deleteRulesetMutation = useDeleteRuleset();

  // Filter rulesets by ownership
  const userRulesets = rulesets.filter((ruleset) => ruleset.user.id === session?.user?.id);
  const publicRulesets = rulesets.filter((ruleset) => ruleset.visibility === 'PUBLIC');

  const stats = {
    totalRulesets: userRulesets.length,
    publicRulesets: userRulesets.filter((r) => r.visibility === 'PUBLIC').length,
    privateRulesets: userRulesets.filter((r) => r.visibility === 'PRIVATE').length,
    totalRules: userRulesets.reduce((acc, ruleset) => acc + ruleset.rules.length, 0),
  };

  const handleCreateRuleset = () => {
    setEditingRuleset(null);
    setShowCreateDialog(true);
  };

  const handleEditRuleset = (ruleset: Ruleset) => {
    setEditingRuleset(ruleset);
    setShowCreateDialog(true);
  };

  const handleSubmitRuleset = async (data: CreateRulesetData | UpdateRulesetData) => {
    try {
      if ('id' in data) {
        await updateRulesetMutation.mutateAsync(data);
      } else {
        await createRulesetMutation.mutateAsync(data);
      }
      setShowCreateDialog(false);
      setEditingRuleset(null);
    } catch (error) {
      console.error('Error saving ruleset:', error);
    }
  };

  const handleDeleteRuleset = async (ruleset: Ruleset) => {
    if (
      window.confirm(
        `Are you sure you want to delete "${ruleset.name}"? This action cannot be undone.`
      )
    ) {
      try {
        await deleteRulesetMutation.mutateAsync(ruleset.id);
      } catch (error) {
        console.error('Error deleting ruleset:', error);
      }
    }
  };

  const handleCancelForm = () => {
    setShowCreateDialog(false);
    setEditingRuleset(null);
  };

  // Temporarily bypass authentication for testing
  const isTestMode = process.env.NODE_ENV === 'development';

  if (!session?.user && !isTestMode) {
    return (
      <Box className="text-center">
        <Heading size="6" mb="4">
          Please sign in to continue
        </Heading>
        <Button asChild>
          <a href="/auth/signin">Sign In</a>
        </Button>
      </Box>
    );
  }

  // Use mock session in test mode
  const effectiveSession =
    session ||
    (isTestMode
      ? {
          user: {
            id: 'test-user-id',
            name: 'Test User',
            email: '<EMAIL>',
            image: null,
          },
        }
      : null);

  // Show loading state for initial load
  if (rulesetsLoading && rulesets.length === 0) {
    return (
      <Box className="text-center">
        <Heading size="6" mb="4">
          Ruleset Management
        </Heading>
        <Text color="gray" mb="8">
          Loading your rulesets...
        </Text>
        <div className="animate-pulse space-y-4">
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div
                key={`rulesets-skeleton-${i}-${Date.now()}`}
                className="h-20 rounded-lg bg-gray-200 dark:bg-gray-700"
              ></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div
                key={`rulesets-large-skeleton-${i}-${Date.now()}`}
                className="h-32 rounded-lg bg-gray-200 dark:bg-gray-700"
              ></div>
            ))}
          </div>
        </div>
      </Box>
    );
  }

  return (
    <Flex direction="column" gap="8">
      {/* Header */}
      <Flex justify="between" align="center" direction={{ initial: 'column', md: 'row' }} gap="4">
        <Box className="text-center md:text-left">
          <Heading size="8" weight="bold" mb="2">
            Ruleset Management
          </Heading>
          <Text size="4" color="gray">
            Create and manage collections of AI rules for quick deployment
          </Text>
        </Box>
        <Button onClick={handleCreateRuleset} size="3">
          <Plus className="mr-2 h-4 w-4" />
          New Ruleset
        </Button>
      </Flex>

      {/* Stats */}
      <Box className="grid grid-cols-2 gap-4 md:grid-cols-4">
        <Card>
          <Flex justify="between" align="center" pb="2">
            <Text size="2" weight="medium" color="gray">
              Total Rulesets
            </Text>
            <Package className="h-4 w-4 text-gray-500" />
          </Flex>
          <Text size="7" weight="bold">
            {stats.totalRulesets}
          </Text>
        </Card>

        <Card>
          <Flex justify="between" align="center" pb="2">
            <Text size="2" weight="medium" color="gray">
              Public Rulesets
            </Text>
            <Users className="h-4 w-4 text-gray-500" />
          </Flex>
          <Text size="7" weight="bold">
            {stats.publicRulesets}
          </Text>
        </Card>

        <Card>
          <Flex justify="between" align="center" pb="2">
            <Text size="2" weight="medium" color="gray">
              Private Rulesets
            </Text>
            <Lock className="h-4 w-4 text-gray-500" />
          </Flex>
          <Text size="7" weight="bold">
            {stats.privateRulesets}
          </Text>
        </Card>

        <Card>
          <Flex justify="between" align="center" pb="2">
            <Text size="2" weight="medium" color="gray">
              Total Rules
            </Text>
            <Eye className="h-4 w-4 text-gray-500" />
          </Flex>
          <Text size="7" weight="bold">
            {stats.totalRules}
          </Text>
        </Card>
      </Box>

      {/* Filters */}
      <Flex gap="4" direction={{ initial: 'column', md: 'row' }}>
        <Box className="flex-1">
          <TextField.Root
            placeholder="Search rulesets..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          >
            <TextField.Slot>
              <Search className="h-4 w-4 text-gray-400" />
            </TextField.Slot>
          </TextField.Root>
        </Box>

        <Select.Root
          value={visibilityFilter}
          onValueChange={(value: 'ALL' | 'PUBLIC' | 'PRIVATE') => setVisibilityFilter(value)}
        >
          <Select.Trigger className="w-full md:w-48">
            {visibilityFilter === 'ALL' ? 'All Visibility' : visibilityFilter}
          </Select.Trigger>
          <Select.Content>
            <Select.Item value="ALL">All Visibility</Select.Item>
            <Select.Item value="PUBLIC">
              <Flex align="center" gap="2">
                <Eye className="h-4 w-4" />
                Public
              </Flex>
            </Select.Item>
            <Select.Item value="PRIVATE">
              <Flex align="center" gap="2">
                <Lock className="h-4 w-4" />
                Private
              </Flex>
            </Select.Item>
          </Select.Content>
        </Select.Root>
      </Flex>

      {/* Content */}
      <Tabs.Root defaultValue="my-rulesets" className="space-y-6">
        <Tabs.List>
          <Tabs.Trigger value="my-rulesets">My Rulesets ({userRulesets.length})</Tabs.Trigger>
          <Tabs.Trigger value="community">Community ({publicRulesets.length})</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="my-rulesets" className="space-y-6">
          {userRulesets.length === 0 ? (
            <Card className="py-12 text-center">
              <Package className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <Heading size="4" mb="2">
                No rulesets yet
              </Heading>
              <Text color="gray" mb="4">
                Create your first ruleset to organize your AI prompt rules
              </Text>
              <Button onClick={handleCreateRuleset}>
                <Plus className="mr-2 h-4 w-4" />
                Create Ruleset
              </Button>
            </Card>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {userRulesets.map((ruleset) => (
                <RulesetCard
                  key={ruleset.id}
                  ruleset={ruleset}
                  onEdit={handleEditRuleset}
                  onDelete={handleDeleteRuleset}
                  showActions={true}
                />
              ))}
            </div>
          )}
        </Tabs.Content>

        <Tabs.Content value="community" className="space-y-6">
          {publicRulesets.length === 0 ? (
            <Card className="py-12 text-center">
              <Users className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <Heading size="4" mb="2">
                No community rulesets found
              </Heading>
              <Text color="gray">Try adjusting your filters or check back later</Text>
            </Card>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {publicRulesets.map((ruleset) => (
                <RulesetCard
                  key={ruleset.id}
                  ruleset={ruleset}
                  showActions={ruleset.user.id === effectiveSession?.user?.id}
                  onEdit={
                    ruleset.user.id === effectiveSession?.user?.id ? handleEditRuleset : undefined
                  }
                  onDelete={
                    ruleset.user.id === effectiveSession?.user?.id
                      ? handleDeleteRuleset
                      : undefined
                  }
                />
              ))}
            </div>
          )}
        </Tabs.Content>
      </Tabs.Root>

      {/* Create/Edit Dialog */}
      <Dialog.Root open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <Dialog.Content className="max-h-[90vh] w-[95vw] max-w-5xl overflow-hidden">
          <Dialog.Title className="sr-only">
            {editingRuleset ? 'Edit Ruleset' : 'Create New Ruleset'}
          </Dialog.Title>
          <div className="max-h-[85vh] overflow-y-auto">
            <RulesetForm
              initialData={editingRuleset || undefined}
              onSubmit={handleSubmitRuleset}
              onCancel={handleCancelForm}
              isLoading={createRulesetMutation.isPending || updateRulesetMutation.isPending}
            />
          </div>
        </Dialog.Content>
      </Dialog.Root>
    </Flex>
  );
}