import { toNextJs<PERSON><PERSON><PERSON> } from 'better-auth/next-js';
import type { NextRequest } from 'next/server';
import { getAuthInstance } from '@/lib/auth';

// Force dynamic rendering to prevent build issues
export const dynamic = 'force-dynamic';

// Get the actual auth instance
const handler = toNextJsHandler(getAuthInstance());

export async function GET(request: NextRequest): Promise<Response> {
  return handler.GET(request);
}

export async function POST(request: NextRequest): Promise<Response> {
  return handler.POST(request);
}
