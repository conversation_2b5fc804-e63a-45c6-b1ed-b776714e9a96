import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// Force dynamic rendering to prevent build issues
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET() {
  // Skip database operations during build
  if (
    process.env.NODE_ENV === 'production' &&
    !process.env.VERCEL &&
    !process.env.RAILWAY_ENVIRONMENT
  ) {
    return NextResponse.json([]);
  }

  try {
    const tags = await prisma.tag.findMany({
      orderBy: { name: 'asc' },
    });

    return NextResponse.json(tags);
  } catch (error) {
    console.error('Error fetching tags:', error);
    return NextResponse.json({ error: 'Failed to fetch tags' }, { status: 500 });
  }
}
