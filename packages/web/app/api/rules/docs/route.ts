/**
 * API route for listing all rules from packages/docs/rules directory
 * GET /api/rules/docs - Returns normalized Rule entities from MDC files
 */

import { NextResponse } from 'next/server';
import { getAllDocsRules } from '@/lib/docs-rule-normalizer';

export async function GET() {
  try {
    // Skip during build time to avoid file system access issues
    const isBuildTime =
      process.env.NODE_ENV === 'production' &&
      !process.env.VERCEL &&
      !process.env.RAILWAY_ENVIRONMENT;

    if (isBuildTime) {
      return NextResponse.json(
        {
          error: 'Service temporarily unavailable during build',
        },
        { status: 503 }
      );
    }

    const rules = getAllDocsRules();

    return NextResponse.json({
      success: true,
      data: rules,
      count: rules.length,
      message: `Found ${rules.length} documentation rules`,
    });
  } catch (error) {
    console.error('Error fetching docs rules:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch documentation rules',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
