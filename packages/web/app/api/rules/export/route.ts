import { type NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { generateMDXContent } from '@/lib/mdx-utils';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  // Skip database operations during build
  const isBuildTime =
    process.env.NODE_ENV === 'production' &&
    !process.env.VERCEL &&
    !process.env.RAILWAY_ENVIRONMENT;
  if (isBuildTime) {
    return NextResponse.json({ error: 'Service temporarily unavailable' }, { status: 503 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const ids = searchParams.get('ids')?.split(',').filter(Boolean) || [];
    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || [];
    const search = searchParams.get('search') || '';

    // Build query conditions
    const where: Record<string, unknown> = {
      visibility: 'PUBLIC',
    };

    // Filter by specific IDs if provided
    if (ids.length > 0) {
      where.id = { in: ids };
    }

    // Filter by tags if provided
    if (tags.length > 0) {
      where.tags = {
        some: {
          tag: {
            name: { in: tags },
          },
        },
      };
    }

    // Search in title and description if search term provided
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const rules = await prisma.rule.findMany({
      where,
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });

    if (rules.length === 0) {
      return NextResponse.json({ error: 'No rules found matching the criteria' }, { status: 404 });
    }

    // Generate MDX content for each rule
    const mdxFiles = rules.map((rule) => ({
      filename: `${sanitizeFilename(rule.title)}.mdx`,
      content: generateMDXContent(rule, { includeId: false, useISODates: false }),
    }));

    // If only one rule, return it directly
    if (mdxFiles.length === 1) {
      return new NextResponse(mdxFiles[0].content, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Content-Disposition': `attachment; filename="${mdxFiles[0].filename}"`,
        },
      });
    }

    // For multiple rules, create a combined MDX file with all rules
    const combinedContent = mdxFiles
      .map((file) => {
        return `<!-- File: ${file.filename} -->\n${file.content}`;
      })
      .join('\n\n<!-- ============================================ -->\n\n');

    const filename = generateBatchFilename(search || undefined, tags, undefined);

    return new NextResponse(combinedContent, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error('Error exporting rules:', error);
    return NextResponse.json({ error: 'Failed to export rules' }, { status: 500 });
  }
}

function sanitizeFilename(filename: string): string {
  // Remove or replace characters that are not safe for filenames
  return filename
    .replace(/[^a-z0-9]/gi, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .toLowerCase()
    .substring(0, 100); // Limit length
}

function generateBatchFilename(search?: string, tags?: string[], ideType?: string): string {
  const parts = ['rules'];

  if (search) {
    parts.push(sanitizeFilename(search));
  }

  if (ideType && ideType !== 'ALL') {
    parts.push(ideType.toLowerCase());
  }

  if (tags && tags.length > 0) {
    parts.push(
      tags
        .slice(0, 3)
        .map((t) => sanitizeFilename(t))
        .join('-')
    );
  }

  const timestamp = new Date().toISOString().split('T')[0];
  parts.push(timestamp);

  return `${parts.join('-')}.mdx`;
}
