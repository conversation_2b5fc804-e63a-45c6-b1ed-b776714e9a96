import { headers } from 'next/headers';
import { type NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';

// Force dynamic rendering to prevent build issues
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  // Skip database operations during build
  const isBuildTime =
    process.env.NODE_ENV === 'production' &&
    !process.env.VERCEL &&
    !process.env.RAILWAY_ENVIRONMENT;
  if (isBuildTime) {
    return NextResponse.json([]);
  }

  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || [];
    const visibility = searchParams.get('visibility');

    const where: Record<string, unknown> = {
      OR: [{ visibility: 'PUBLIC' }, session?.user ? { userId: session.user.id } : {}],
    };

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (tags.length > 0) {
      where.tags = {
        some: {
          tag: {
            name: { in: tags },
          },
        },
      };
    }

    if (visibility) {
      where.visibility = visibility;
    }

    const rules = await prisma.rule.findMany({
      where,
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });

    return NextResponse.json(rules);
  } catch (error) {
    console.error('Error fetching rules:', error);
    return NextResponse.json({ error: 'Failed to fetch rules' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { title, description, content, visibility, applyType, glob, tags } = body;

    const rule = await prisma.rule.create({
      data: {
        title,
        description,
        content,
        visibility,
        applyType: applyType || 'manual', // Default to manual if not provided
        glob: glob || null,
        userId: session.user.id,
        shareToken: visibility === 'PUBLIC' ? crypto.randomUUID() : null,
      },
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
      },
    });

    // Connect tags
    if (tags && tags.length > 0) {
      for (const tagName of tags) {
        let tag = await prisma.tag.findUnique({
          where: { name: tagName },
        });

        if (!tag) {
          tag = await prisma.tag.create({
            data: { name: tagName },
          });
        }

        await prisma.ruleTag.create({
          data: {
            ruleId: rule.id,
            tagId: tag.id,
          },
        });
      }
    }

    return NextResponse.json(rule);
  } catch (error) {
    console.error('Error creating rule:', error);
    return NextResponse.json({ error: 'Failed to create rule' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, title, description, content, visibility, applyType, tags } = body;

    if (!id) {
      return NextResponse.json({ error: 'Rule ID is required' }, { status: 400 });
    }

    // Check if the rule exists and belongs to the user
    const existingRule = await prisma.rule.findUnique({
      where: { id },
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
      },
    });

    if (!existingRule) {
      return NextResponse.json({ error: 'Rule not found' }, { status: 404 });
    }

    if (existingRule.userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Update the rule
    const _updatedRule = await prisma.rule.update({
      where: { id },
      data: {
        title,
        description,
        content,
        visibility,
        applyType: applyType || existingRule.applyType || 'manual', // Keep existing or default to manual
        shareToken: visibility === 'PUBLIC' ? crypto.randomUUID() : null,
      },
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Remove existing tags
    await prisma.ruleTag.deleteMany({
      where: { ruleId: id },
    });

    // Connect new tags
    if (tags && tags.length > 0) {
      for (const tagName of tags) {
        let tag = await prisma.tag.findUnique({
          where: { name: tagName },
        });

        if (!tag) {
          tag = await prisma.tag.create({
            data: { name: tagName },
          });
        }

        await prisma.ruleTag.create({
          data: {
            ruleId: id,
            tagId: tag.id,
          },
        });
      }
    }

    // Fetch the updated rule with tags
    const finalRule = await prisma.rule.findUnique({
      where: { id },
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(finalRule);
  } catch (error) {
    console.error('Error updating rule:', error);
    return NextResponse.json({ error: 'Failed to update rule' }, { status: 500 });
  }
}
