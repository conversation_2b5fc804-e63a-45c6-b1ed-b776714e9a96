import { headers } from 'next/headers';
import { type NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';

// Force dynamic rendering to prevent build issues
export const dynamic = 'force-dynamic';

export async function GET(_request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Skip database operations during build
    const isBuildTime =
      process.env.NODE_ENV === 'production' &&
      !process.env.VERCEL &&
      !process.env.RAILWAY_ENVIRONMENT;
    if (isBuildTime) {
      return NextResponse.json({ error: 'Service temporarily unavailable' }, { status: 503 });
    }

    const session = await auth.api.getSession({
      headers: await headers(),
    });

    const rule = await prisma.rule.findUnique({
      where: { id: params.id },
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!rule) {
      return NextResponse.json({ error: 'Rule not found' }, { status: 404 });
    }

    // Check if user can access this rule
    const canAccess =
      rule.visibility === 'PUBLIC' || (session?.user && rule.userId === session.user.id);

    if (!canAccess) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    return NextResponse.json(rule);
  } catch (error) {
    console.error('Error fetching rule:', error);
    return NextResponse.json({ error: 'Failed to fetch rule' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const rule = await prisma.rule.findUnique({
      where: { id: params.id },
    });

    if (!rule) {
      return NextResponse.json({ error: 'Rule not found' }, { status: 404 });
    }

    if (rule.userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { title, description, content, visibility, applyType, glob, tags } = body;

    // Update the rule
    const _updatedRule = await prisma.rule.update({
      where: { id: params.id },
      data: {
        title,
        description,
        content,
        visibility,
        applyType: applyType || rule.applyType || 'manual', // Keep existing or default to manual
        glob: glob || null,
        shareToken: visibility === 'PUBLIC' ? rule.shareToken || crypto.randomUUID() : null,
      },
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
      },
    });

    // Handle tags update
    if (tags && Array.isArray(tags)) {
      // Remove existing tags
      await prisma.ruleTag.deleteMany({
        where: { ruleId: params.id },
      });

      // Add new tags
      for (const tagName of tags) {
        let tag = await prisma.tag.findUnique({
          where: { name: tagName },
        });

        if (!tag) {
          tag = await prisma.tag.create({
            data: { name: tagName },
          });
        }

        await prisma.ruleTag.create({
          data: {
            ruleId: params.id,
            tagId: tag.id,
          },
        });
      }
    }

    // Fetch the updated rule with tags
    const finalRule = await prisma.rule.findUnique({
      where: { id: params.id },
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
      },
    });

    return NextResponse.json(finalRule);
  } catch (error) {
    console.error('Error updating rule:', error);
    return NextResponse.json({ error: 'Failed to update rule' }, { status: 500 });
  }
}

export async function DELETE(_request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const rule = await prisma.rule.findUnique({
      where: { id: params.id },
    });

    if (!rule) {
      return NextResponse.json({ error: 'Rule not found' }, { status: 404 });
    }

    if (rule.userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Delete the rule (cascade will handle tags)
    await prisma.rule.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting rule:', error);
    return NextResponse.json({ error: 'Failed to delete rule' }, { status: 500 });
  }
}
