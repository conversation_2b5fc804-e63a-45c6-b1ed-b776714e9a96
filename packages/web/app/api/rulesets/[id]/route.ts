import { headers } from 'next/headers';
import { type NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';

// Force dynamic rendering to prevent build issues
export const dynamic = 'force-dynamic';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  // Skip database operations during build
  const isBuildTime =
    process.env.NODE_ENV === 'production' &&
    !process.env.VERCEL &&
    !process.env.RAILWAY_ENVIRONMENT;
  if (isBuildTime) {
    return NextResponse.json({ error: 'Not found' }, { status: 404 });
  }

  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    const { searchParams } = new URL(request.url);
    const shareToken = searchParams.get('token');

    let whereCondition: Record<string, unknown>;
    if (shareToken) {
      // Access via share token
      whereCondition = {
        id: params.id,
        shareToken,
        visibility: 'PUBLIC' as const,
      };
    } else {
      // Normal access
      whereCondition = {
        id: params.id,
        OR: [{ visibility: 'PUBLIC' as const }, session?.user ? { userId: session.user.id } : {}],
      };
    }

    const ruleset = await prisma.ruleset.findFirst({
      where: whereCondition,
      include: {
        rules: {
          include: {
            rule: {
              include: {
                tags: {
                  include: {
                    tag: true,
                  },
                },
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
          orderBy: { order: 'asc' },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!ruleset) {
      return NextResponse.json({ error: 'Ruleset not found' }, { status: 404 });
    }

    return NextResponse.json(ruleset);
  } catch (error) {
    console.error('Error fetching ruleset:', error);
    return NextResponse.json({ error: 'Failed to fetch ruleset' }, { status: 500 });
  }
}

// Helper function to update ruleset basic info
async function updateRulesetBasicInfo(
  rulesetId: string,
  body: { name?: string; description?: string; visibility?: string },
  existingRuleset: { shareToken: string | null }
) {
  const updateData: Record<string, unknown> = {};
  if (body.name !== undefined) updateData.name = body.name.trim();
  if (body.description !== undefined) updateData.description = body.description?.trim() || null;
  if (body.visibility !== undefined) {
    updateData.visibility = body.visibility;
    updateData.shareToken =
      body.visibility === 'PUBLIC' ? existingRuleset.shareToken || crypto.randomUUID() : null;
  }

  return await prisma.ruleset.update({
    where: { id: rulesetId },
    data: updateData,
  });
}

// Helper function to update ruleset rules
async function updateRulesetRules(
  rulesetId: string,
  ruleIds: string[] | undefined,
  userId: string
) {
  if (ruleIds === undefined || !Array.isArray(ruleIds)) return;

  // Remove existing rule associations
  await prisma.rulesetRule.deleteMany({
    where: { rulesetId },
  });

  if (ruleIds.length > 0) {
    // Verify that all rules exist and user has access to them
    const rules = await prisma.rule.findMany({
      where: {
        id: { in: ruleIds },
        OR: [{ userId }, { visibility: 'PUBLIC' }],
      },
    });

    if (rules.length !== ruleIds.length) {
      throw new Error('Some rules not found or access denied');
    }

    // Create new ruleset-rule relationships
    await Promise.all(
      ruleIds.map((ruleId: string, index: number) =>
        prisma.rulesetRule.create({
          data: {
            rulesetId,
            ruleId,
            order: index,
          },
        })
      )
    );
  }
}

// Helper function to fetch complete ruleset
async function fetchCompleteRuleset(rulesetId: string) {
  return await prisma.ruleset.findUnique({
    where: { id: rulesetId },
    include: {
      rules: {
        include: {
          rule: {
            include: {
              tags: {
                include: {
                  tag: true,
                },
              },
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
        orderBy: { order: 'asc' },
      },
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, visibility, ruleIds } = body;

    // Check if user owns the ruleset
    const existingRuleset = await prisma.ruleset.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!existingRuleset) {
      return NextResponse.json({ error: 'Ruleset not found or access denied' }, { status: 404 });
    }

    // Update ruleset basic info
    await updateRulesetBasicInfo(params.id, { name, description, visibility }, existingRuleset);

    // Update rules if provided
    await updateRulesetRules(params.id, ruleIds, session.user.id);

    // Fetch the complete updated ruleset
    const completeRuleset = await fetchCompleteRuleset(params.id);

    return NextResponse.json(completeRuleset);
  } catch (error) {
    console.error('Error updating ruleset:', error);
    return NextResponse.json({ error: 'Failed to update ruleset' }, { status: 500 });
  }
}

export async function DELETE(_request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user owns the ruleset
    const existingRuleset = await prisma.ruleset.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!existingRuleset) {
      return NextResponse.json({ error: 'Ruleset not found or access denied' }, { status: 404 });
    }

    // Delete the ruleset (cascade will handle ruleset_rules)
    await prisma.ruleset.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting ruleset:', error);
    return NextResponse.json({ error: 'Failed to delete ruleset' }, { status: 500 });
  }
}
