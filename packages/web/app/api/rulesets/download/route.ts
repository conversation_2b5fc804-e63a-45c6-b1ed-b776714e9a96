import { type NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { generateMDXContent, type RuleWithRelations } from '@/lib/mdx-utils';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  // Skip database operations during build
  const isBuildTime =
    process.env.NODE_ENV === 'production' &&
    !process.env.VERCEL &&
    !process.env.RAILWAY_ENVIRONMENT;
  if (isBuildTime) {
    return NextResponse.json({ error: 'Service temporarily unavailable' }, { status: 503 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const rulesetId = searchParams.get('id');
    const format = searchParams.get('format') || 'mdx';

    // If a specific ruleset ID is provided, return just that ruleset
    if (rulesetId) {
      const ruleset = await prisma.ruleset.findFirst({
        where: {
          id: rulesetId,
          visibility: 'PUBLIC',
        },
        include: {
          rules: {
            include: {
              rule: {
                include: {
                  tags: {
                    include: {
                      tag: true,
                    },
                  },
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
            orderBy: { order: 'asc' },
          },
          user: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!ruleset) {
        return NextResponse.json({ error: 'Ruleset not found or not public' }, { status: 404 });
      }

      if (format === 'json') {
        // Return as JSON for programmatic access
        return NextResponse.json(ruleset);
      }

      // Generate MDX content for the ruleset
      const mdxContent = generateRulesetMDXContent(ruleset);

      // Return as downloadable file
      return new NextResponse(mdxContent, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Content-Disposition': `attachment; filename="${sanitizeFilename(ruleset.name)}-ruleset.mdx"`,
        },
      });
    }

    // If no specific ID, return all public rulesets
    const rulesets = await prisma.ruleset.findMany({
      where: {
        visibility: 'PUBLIC',
      },
      include: {
        rules: {
          include: {
            rule: {
              include: {
                tags: {
                  include: {
                    tag: true,
                  },
                },
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
          orderBy: { order: 'asc' },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });

    if (format === 'json') {
      // Return as JSON for programmatic access
      return NextResponse.json(rulesets);
    }

    // Generate combined MDX content for all rulesets
    const combinedMDX = rulesets
      .map((ruleset) => generateRulesetMDXContent(ruleset))
      .join('\n\n---\n\n');

    return new NextResponse(combinedMDX, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Content-Disposition': 'attachment; filename="public-rulesets.mdx"',
      },
    });
  } catch (error) {
    console.error('Error downloading rulesets:', error);
    return NextResponse.json({ error: 'Failed to download rulesets' }, { status: 500 });
  }
}

function generateRulesetMDXContent(ruleset: Record<string, unknown>): string {
  const author = (ruleset.user as { name?: string })?.name || 'Unknown';
  const date = new Date(ruleset.createdAt as string).toLocaleDateString();
  const ruleCount = (ruleset.rules as Array<unknown>).length;

  // Get all unique tags from rules in the ruleset
  const allTags = (
    ruleset.rules as Array<{ rule: { tags: Array<{ tag: { id: string; name: string } }> } }>
  ).flatMap((r) => r.rule.tags.map((t) => t.tag));
  const uniqueTags = allTags.filter(
    (tag, index: number, self) => index === self.findIndex((t) => t.id === tag.id)
  );
  const tags = uniqueTags.map((t) => t.name).join(', ');

  // Generate ruleset header with metadata
  const rulesetHeader = `---
title: "${ruleset.name}"
description: "${ruleset.description || ''}"
author: "${author}"
date: "${date}"
ruleCount: ${ruleCount}
tags: [${uniqueTags.map((t) => `"${t.name}"`).join(', ')}]
type: "ruleset"
---

# ${ruleset.name}

${ruleset.description ? `> ${ruleset.description}\n\n` : ''}

**Author:** ${author}
**Created:** ${date}
**Rules:** ${ruleCount}
${tags ? `**Tags:** ${tags}  ` : ''}

## Rules in this Ruleset

`;

  // Generate individual rule content using the existing generateMDXContent function
  const rulesContent = (ruleset.rules as Array<{ rule: { title: string; content: string } }>)
    .map((rulesetRule, index: number) => {
      const rule = rulesetRule.rule;
      return `### ${index + 1}. ${rule.title}

${generateMDXContent(rule as RuleWithRelations, { includeId: false, useISODates: false })}`;
    })
    .join('\n\n---\n\n');

  return rulesetHeader + rulesContent;
}

function sanitizeFilename(filename: string): string {
  // Remove or replace characters that are not safe for filenames
  return filename
    .replace(/[^a-z0-9]/gi, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .toLowerCase()
    .substring(0, 100); // Limit length
}
