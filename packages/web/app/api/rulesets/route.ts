import { headers } from 'next/headers';
import { type NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';

// Force dynamic rendering to prevent build issues
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  // Skip database operations during build
  const isBuildTime =
    process.env.NODE_ENV === 'production' &&
    !process.env.VERCEL &&
    !process.env.RAILWAY_ENVIRONMENT;
  if (isBuildTime) {
    return NextResponse.json([]);
  }

  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const visibility = searchParams.get('visibility');

    const where: Record<string, unknown> = {
      OR: [{ visibility: 'PUBLIC' }, session?.user ? { userId: session.user.id } : {}],
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (visibility) {
      where.visibility = visibility;
    }

    const rulesets = await prisma.ruleset.findMany({
      where,
      include: {
        rules: {
          include: {
            rule: {
              include: {
                tags: {
                  include: {
                    tag: true,
                  },
                },
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
          orderBy: { order: 'asc' },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });

    return NextResponse.json(rulesets);
  } catch (error) {
    console.error('Error fetching rulesets:', error);
    return NextResponse.json({ error: 'Failed to fetch rulesets' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, visibility, ruleIds } = body;

    if (!name || !name.trim()) {
      return NextResponse.json({ error: 'Ruleset name is required' }, { status: 400 });
    }

    // Create the ruleset
    const ruleset = await prisma.ruleset.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        visibility: visibility || 'PRIVATE',
        userId: session.user.id,
        shareToken: visibility === 'PUBLIC' ? crypto.randomUUID() : null,
      },
    });

    // Add rules to the ruleset if provided
    if (ruleIds && Array.isArray(ruleIds) && ruleIds.length > 0) {
      // Verify that all rules exist and user has access to them
      const rules = await prisma.rule.findMany({
        where: {
          id: { in: ruleIds },
          OR: [{ userId: session.user.id }, { visibility: 'PUBLIC' }],
        },
      });

      if (rules.length !== ruleIds.length) {
        // Clean up the created ruleset
        await prisma.ruleset.delete({
          where: { id: ruleset.id },
        });
        return NextResponse.json(
          { error: 'Some rules not found or access denied' },
          { status: 400 }
        );
      }

      // Create ruleset-rule relationships
      await Promise.all(
        ruleIds.map((ruleId: string, index: number) =>
          prisma.rulesetRule.create({
            data: {
              rulesetId: ruleset.id,
              ruleId,
              order: index,
            },
          })
        )
      );
    }

    // Fetch the complete ruleset with rules
    const completeRuleset = await prisma.ruleset.findUnique({
      where: { id: ruleset.id },
      include: {
        rules: {
          include: {
            rule: {
              include: {
                tags: {
                  include: {
                    tag: true,
                  },
                },
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
          orderBy: { order: 'asc' },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(completeRuleset);
  } catch (error) {
    console.error('Error creating ruleset:', error);
    return NextResponse.json({ error: 'Failed to create ruleset' }, { status: 500 });
  }
}
