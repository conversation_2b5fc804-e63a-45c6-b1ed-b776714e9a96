import type { MetadataRoute } from 'next';
import prisma from '@/lib/prisma';

// Force dynamic generation
export const dynamic = 'force-dynamic';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://onlyrules.app';

  // During build, return minimal sitemap to avoid database issues
  const isBuildTime =
    process.env.NODE_ENV === 'production' &&
    !process.env.VERCEL &&
    !process.env.RAILWAY_ENVIRONMENT;
  if (isBuildTime) {
    console.log('Build environment detected, returning static sitemap only');
  }

  // IDE pages - matching actual page structure
  const idePages = [
    'cursor',
    'windsurf',
    'github-copilot',
    'claude',
    'cline',
    'augment',
    // Note: gemini, openai-codex, tencent-codebuddy don't have actual pages yet
  ];

  // Documentation pages structure
  const docPages = [
    'docs', // Main docs page
    'docs/getting-started/installation',
    'docs/getting-started/quick-start',
    'docs/getting-started/configuration',
    'docs/ides/cursor',
    'docs/ides/windsurf',
    'docs/ides/github-copilot',
    'docs/ides/claude',
    'docs/ides/cline',
    'docs/ides/augment',
    'docs/api/overview',
    'docs/api/rules',
    'docs/api/authentication',
    'docs/guides/seo-setup',
    'docs/guides/i18n',
    'docs/guides/radix-ui-theme',
    'docs/guides/migration',
  ];

  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/templates`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/rules`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/rulesets`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/ides`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/dashboard`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/auth/signin`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/auth/signup`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
  ];

  // Generate IDE pages sitemap
  const idePagesSitemap = idePages.map((ide) => ({
    url: `${baseUrl}/ides/${ide}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));

  // Generate documentation pages sitemap
  const docPagesSitemap = docPages.map((docPath) => ({
    url: `${baseUrl}/${docPath}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: docPath === 'docs' ? 0.9 : 0.7,
  }));

  // Return static pages only during build
  if (isBuildTime) {
    return [...staticPages, ...idePagesSitemap, ...docPagesSitemap];
  }

  try {
    // Skip database operations during build if no DATABASE_URL is available
    if (!process.env.DATABASE_URL) {
      console.warn('No DATABASE_URL available, returning static sitemap only');
      return [...staticPages, ...idePagesSitemap, ...docPagesSitemap];
    }

    // Get all public rules for sitemap
    const publicRules = await prisma.rule.findMany({
      where: { visibility: 'PUBLIC' },
      select: {
        id: true,
        updatedAt: true,
      },
      orderBy: { updatedAt: 'desc' },
      take: 1000, // Limit to prevent huge sitemaps
    });

    const rulePages = publicRules.map((rule) => ({
      url: `${baseUrl}/rules/${rule.id}`,
      lastModified: rule.updatedAt,
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }));

    // Get all public rulesets for sitemap
    const publicRulesets = await prisma.ruleset.findMany({
      where: { visibility: 'PUBLIC' },
      select: {
        id: true,
        updatedAt: true,
      },
      orderBy: { updatedAt: 'desc' },
      take: 500, // Limit to prevent huge sitemaps
    });

    const rulesetPages = publicRulesets.map((ruleset) => ({
      url: `${baseUrl}/rulesets/${ruleset.id}`,
      lastModified: ruleset.updatedAt,
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }));

    // Get users with public rules for profile pages
    const usersWithPublicRules = await prisma.user.findMany({
      where: {
        rules: {
          some: {
            visibility: 'PUBLIC',
          },
        },
      },
      select: {
        id: true,
        updatedAt: true,
      },
      take: 500, // Limit to prevent huge sitemaps
    });

    const profilePages = usersWithPublicRules.map((user) => ({
      url: `${baseUrl}/profile/${user.id}`,
      lastModified: user.updatedAt,
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    }));

    return [
      ...staticPages,
      ...idePagesSitemap,
      ...docPagesSitemap,
      ...rulePages,
      ...rulesetPages,
      ...profilePages,
    ];
  } catch (error) {
    console.warn('Failed to generate dynamic sitemap entries:', error);
    // Return static pages only if database is not available
    return [...staticPages, ...idePagesSitemap, ...docPagesSitemap];
  }
}
