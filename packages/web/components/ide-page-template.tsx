'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Callout, Card, Flex, Tabs } from '@radix-ui/themes';
import {
  <PERSON>R<PERSON>,
  BookOpen,
  CheckCircle2,
  Code2,
  Copy,
  Download,
  FileText,
  Lightbulb,
  Settings,
  Terminal,
  Zap,
} from 'lucide-react';
import { useState } from 'react';

interface IDEPageProps {
  ide: {
    name: string;
    icon: string;
    color: string;
    description: string;
    website: string;
    version: string;
  };
  installation: {
    steps: Array<{
      title: string;
      description: string;
      command?: string;
      note?: string;
    }>;
  };
  integration: {
    steps: Array<{
      title: string;
      description: string;
      code?: string;
      image?: string;
    }>;
  };
  features: Array<{
    title: string;
    description: string;
    icon: React.ReactNode;
  }>;
  examples: Array<{
    title: string;
    description: string;
    prompt: string;
    result?: string;
  }>;
  tips: string[];
}

export default function IDEPageTemplate({
  ide,
  installation,
  integration,
  features,
  examples,
  tips,
}: IDEPageProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const copyToClipboard = (text: string, index: number) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Hero Section */}
      <div className="mb-12">
        <Flex align="center" gap="4" mb="6">
          <Box className={`rounded-lg p-4 ${ide.color} bg-opacity-10`}>
            <span className="text-5xl">{ide.icon}</span>
          </Box>
          <Box>
            <h1 className="mb-2 font-bold text-4xl">{ide.name} Integration Guide</h1>
            <p className="text-muted-foreground text-xl">{ide.description}</p>
          </Box>
        </Flex>

        <Flex align="center" gap="4">
          <Badge variant="outline">Version {ide.version}</Badge>
          <a href={ide.website} target="_blank" rel="noopener noreferrer">
            <Button variant="outline" size="2">
              Visit Official Website
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </a>
        </Flex>
      </div>

      {/* Quick Start Alert */}
      <Callout.Root className="mb-8">
        <Callout.Icon>
          <Zap className="h-4 w-4" />
        </Callout.Icon>
        <Callout.Text>
          <strong>Quick Start:</strong> Get up and running with {ide.name} + OnlyRules in just a few
          minutes. Follow our step-by-step guide below to enhance your AI coding experience.
        </Callout.Text>
      </Callout.Root>

      {/* Main Content Tabs */}
      <Tabs.Root defaultValue="installation" className="space-y-8">
        <Tabs.List className="grid w-full grid-cols-4 lg:w-[600px]">
          <Tabs.Trigger value="installation">Installation</Tabs.Trigger>
          <Tabs.Trigger value="integration">Integration</Tabs.Trigger>
          <Tabs.Trigger value="examples">Examples</Tabs.Trigger>
          <Tabs.Trigger value="tips">Best Practices</Tabs.Trigger>
        </Tabs.List>

        {/* Installation Tab */}
        <Tabs.Content value="installation" className="space-y-6">
          <Card>
            <div className="mb-4 space-y-2">
              <div className="flex items-center gap-2 font-semibold text-lg">
                <Download className="h-5 w-5" />
                Installing {ide.name}
              </div>
              <div className="text-muted-foreground text-sm">
                Follow these steps to install and set up {ide.name} on your system
              </div>
            </div>
            <div className="space-y-6">
              {installation.steps.map((step, index) => (
                <div key={`installation-${step.title}-${index}`} className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div
                      className={`mt-1 h-8 w-8 rounded-full ${ide.color} flex flex-shrink-0 items-center justify-center bg-opacity-20`}
                    >
                      <span className="font-semibold text-sm">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="mb-1 font-semibold">{step.title}</h4>
                      <p className="text-muted-foreground">{step.description}</p>

                      {step.command && (
                        <div className="relative mt-3">
                          <pre className="overflow-x-auto rounded-lg bg-muted p-4">
                            <code>{step.command}</code>
                          </pre>
                          <Button
                            size="1"
                            variant="ghost"
                            className="absolute top-2 right-2"
                            onClick={() => copyToClipboard(step.command || '', index)}
                          >
                            {copiedIndex === index ? (
                              <CheckCircle2 className="h-4 w-4 text-green-500" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      )}

                      {step.note && (
                        <Callout.Root className="mt-3">
                          <Callout.Icon>
                            <Lightbulb className="h-4 w-4" />
                          </Callout.Icon>
                          <Callout.Text>{step.note}</Callout.Text>
                        </Callout.Root>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Tabs.Content>

        {/* Integration Tab */}
        <Tabs.Content value="integration" className="space-y-6">
          <Card>
            <div className="mb-4 space-y-2">
              <div className="flex items-center gap-2 font-semibold text-lg">
                <Settings className="h-5 w-5" />
                Integrating OnlyRules with {ide.name}
              </div>
              <div className="text-muted-foreground text-sm">
                Connect OnlyRules to enhance your AI coding experience
              </div>
            </div>
            <div className="space-y-6">
              {integration.steps.map((step, index) => (
                <div key={`integration-${step.title}-${index}`} className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div
                      className={`mt-1 h-8 w-8 rounded-full ${ide.color} flex flex-shrink-0 items-center justify-center bg-opacity-20`}
                    >
                      <span className="font-semibold text-sm">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="mb-1 font-semibold">{step.title}</h4>
                      <p className="text-muted-foreground">{step.description}</p>

                      {step.code && (
                        <div className="relative mt-3">
                          <pre className="overflow-x-auto rounded-lg bg-muted p-4">
                            <code>{step.code}</code>
                          </pre>
                          <Button
                            size="1"
                            variant="ghost"
                            className="absolute top-2 right-2"
                            onClick={() => copyToClipboard(step.code || '', index + 100)}
                          >
                            {copiedIndex === index + 100 ? (
                              <CheckCircle2 className="h-4 w-4 text-green-500" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Features Section */}
          <div className="grid gap-4 md:grid-cols-3">
            {features.map((feature, index) => (
              <Card key={`feature-${feature.title}-${index}`}>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 font-semibold text-lg">
                    {feature.icon}
                    {feature.title}
                  </div>
                  <div className="text-muted-foreground text-sm">{feature.description}</div>
                </div>
              </Card>
            ))}
          </div>
        </Tabs.Content>

        {/* Examples Tab */}
        <Tabs.Content value="examples" className="space-y-6">
          <Card>
            <div className="mb-4 space-y-2">
              <div className="flex items-center gap-2 font-semibold text-lg">
                <Code2 className="h-5 w-5" />
                Example Prompts for {ide.name}
              </div>
              <div className="text-muted-foreground text-sm">
                Try these optimized prompts to get the most out of {ide.name} with OnlyRules
              </div>
            </div>
            <div className="space-y-6">
              {examples.map((example, index) => (
                <div
                  key={`example-${example.title}-${index}`}
                  className="space-y-3 rounded-lg border p-4"
                >
                  <h4 className="font-semibold">{example.title}</h4>
                  <p className="text-muted-foreground text-sm">{example.description}</p>

                  <div className="relative">
                    <pre className="overflow-x-auto rounded-lg bg-muted p-4 text-sm">
                      <code>{example.prompt}</code>
                    </pre>
                    <Button
                      size="1"
                      variant="ghost"
                      className="absolute top-2 right-2"
                      onClick={() => copyToClipboard(example.prompt, index + 200)}
                    >
                      {copiedIndex === index + 200 ? (
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  {example.result && (
                    <div className="mt-3">
                      <p className="mb-2 font-medium text-sm">Expected Result:</p>
                      <p className="text-muted-foreground text-sm">{example.result}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </Card>
        </Tabs.Content>

        {/* Best Practices Tab */}
        <Tabs.Content value="tips" className="space-y-6">
          <Card>
            <div className="mb-4 space-y-2">
              <div className="flex items-center gap-2 font-semibold text-lg">
                <BookOpen className="h-5 w-5" />
                Best Practices for {ide.name}
              </div>
              <div className="text-muted-foreground text-sm">
                Tips and tricks to maximize your productivity with {ide.name} and OnlyRules
              </div>
            </div>
            <div>
              <ul className="space-y-4">
                {tips.map((tip) => (
                  <li key={tip} className="flex items-start gap-3">
                    <div
                      className={`mt-0.5 h-6 w-6 rounded-full ${ide.color} flex flex-shrink-0 items-center justify-center bg-opacity-20`}
                    >
                      <span className="text-xs">✓</span>
                    </div>
                    <span className="text-muted-foreground">{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          </Card>

          {/* Additional Resources */}
          <Card>
            <div className="mb-4 space-y-2">
              <div className="flex items-center gap-2 font-semibold text-lg">
                <FileText className="h-5 w-5" />
                Additional Resources
              </div>
            </div>
            <div className="space-y-3">
              <a href="/templates" className="flex items-center gap-2 text-primary hover:underline">
                <Terminal className="h-4 w-4" />
                Browse {ide.name} prompt templates
              </a>
              <a href="/dashboard" className="flex items-center gap-2 text-primary hover:underline">
                <Settings className="h-4 w-4" />
                Manage your rules
              </a>
            </div>
          </Card>
        </Tabs.Content>
      </Tabs.Root>
    </div>
  );
}
