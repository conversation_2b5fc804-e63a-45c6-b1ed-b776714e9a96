'use client';

import {
  Avatar,
  Box,
  Button,
  Container,
  DropdownMenu,
  Flex,
  IconButton,
  Separator,
  Text,
} from '@radix-ui/themes';
import {
  Code,
  FileText,
  Github as GitHubIcon,
  Home,
  LogIn,
  LogOut,
  Menu,
  Moon,
  Settings,
  Sun,
  User,
} from 'lucide-react';
import Link from 'next/link';
import { useTheme } from 'next-themes';
import { useEffect, useRef, useState } from 'react';
import { IDEPreferenceManager } from '@/components/ide-preferences/ide-preference-manager';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer';
import { LanguageSwitcher } from '@/components/ui/language-switcher';
import { signOut, useSession } from '@/lib/auth-client';
import type { Locale } from '@/lib/i18n';

// import { useLingui } from '@lingui/react';

interface NavbarProps {
  locale: Locale;
}

export function Navbar({ locale }: NavbarProps) {
  const { theme, setTheme } = useTheme();
  const { data: session } = useSession();
  const [showIDEPreferences, setShowIDEPreferences] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const drawerTriggerRef = useRef<HTMLButtonElement>(null);

  // Define navbar height as a constant for consistent usage
  const NAVBAR_HEIGHT = '64px'; // Increased from 56px for better mobile spacing

  // const { i18n } = useLingui();

  const handleSignOut = async () => {
    await signOut();
    setDropdownOpen(false);
    setMobileMenuOpen(false);
  };

  const handleIDEPreferences = () => {
    setShowIDEPreferences(true);
    setDropdownOpen(false);
    setMobileMenuOpen(false);
  };

  const handleNavigation = () => {
    setDropdownOpen(false);
    setMobileMenuOpen(false);
  };

  const _toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Enhanced keyboard navigation and focus management
  useEffect(() => {
    if (!mobileMenuOpen && drawerTriggerRef.current) {
      // Return focus to trigger when drawer closes
      drawerTriggerRef.current.focus();
    }
  }, [mobileMenuOpen]);

  // Handle escape key to close drawer
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    if (mobileMenuOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [mobileMenuOpen]);

  const navigationLinks = [
    { href: '/dashboard', label: 'Dashboard' },
    { href: '/rules', label: 'My Rules' },
    // { href: '/rulesets', label: 'Rulesets' },
    { href: '/rulesets', label: 'Ruleset Library' },
    { href: '/ides', label: 'IDEs' },
    { href: '/docs', label: 'Docs' },
  ];

  return (
    <>
      <Box
        asChild
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 50,
          width: '100%',
          borderBottom: '1px solid var(--gray-6)',
          backgroundColor: 'var(--color-panel-translucent)',
          backdropFilter: 'blur(8px)',
        }}
      >
        <nav>
          <Container size="4" className="mobile-container">
            <Flex align="center" justify="between" height={NAVBAR_HEIGHT} gap="4">
              {/* Logo and Desktop Navigation */}
              <Flex align="center" gap={{ initial: '3', xs: '4', sm: '8' }}>
                <Link href="/" onClick={handleNavigation}>
                  <Flex align="center" gap="2" asChild>
                    <Box>
                      <Code size={20} className="xs:h-6 xs:w-6 text-[var(--accent-9)]" />
                      <Text
                        size={{ initial: '4', xs: '5' }}
                        weight="bold"
                        color="gray"
                        highContrast
                        style={{ whiteSpace: 'nowrap' }}
                      >
                        OnlyRules
                      </Text>
                    </Box>
                  </Flex>
                </Link>

                {/* Desktop Navigation */}
                <Box display={{ initial: 'none', md: 'block' }}>
                  <Flex align="center" gap="6">
                    {navigationLinks.map((link) => (
                      <Link key={link.href} href={link.href} onClick={handleNavigation}>
                        <Text
                          size="2"
                          weight="medium"
                          className="transition-colors hover:text-[var(--accent-9)]"
                        >
                          {link.label}
                        </Text>
                      </Link>
                    ))}
                  </Flex>
                </Box>
              </Flex>

              {/* Desktop Controls */}
              <Flex align="center" gap={{ initial: '2', xs: '3' }}>
                {/* Mobile Theme Toggle - Always visible on mobile */}
                <Box display={{ sm: 'none', initial: 'block' }}>
                  <IconButton
                    variant="ghost"
                    size="2"
                    onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                    className="touch-target mobile-theme-toggle"
                    aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`}
                    title={`Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`}
                  >
                    <Box style={{ position: 'relative' }}>
                      <Sun
                        size={18}
                        className="dark:-rotate-90 rotate-0 scale-100 transition-all dark:scale-0"
                        aria-hidden="true"
                      />
                      <Moon
                        size={18}
                        className="absolute top-0 left-0 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"
                        aria-hidden="true"
                      />
                    </Box>
                  </IconButton>
                </Box>

                <Box display={{ initial: 'none', sm: 'block' }}>
                  <Flex align="center" gap={{ initial: '2', xs: '3' }}>
                    <LanguageSwitcher currentLocale={locale} />

                    <Button variant="ghost" size="2" asChild className="touch-target">
                      <Link
                        href="https://github.com/ranglang/onlyrules"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Flex align="center" gap="2">
                          <GitHubIcon size={16} />
                          <Box display={{ initial: 'none', md: 'block' }}>
                            <Text size="2">GitHub</Text>
                          </Box>
                        </Flex>
                      </Link>
                    </Button>

                    {/* Desktop Theme Toggle */}
                    <IconButton
                      variant="ghost"
                      size="2"
                      onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                      className="touch-target"
                      aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`}
                      title={`Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`}
                    >
                      <Box style={{ position: 'relative' }}>
                        <Sun
                          size={16}
                          className="dark:-rotate-90 rotate-0 scale-100 transition-all dark:scale-0"
                          aria-hidden="true"
                        />
                        <Moon
                          size={16}
                          className="absolute top-0 left-0 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"
                          aria-hidden="true"
                        />
                      </Box>
                    </IconButton>

                    {session?.user ? (
                      <DropdownMenu.Root open={dropdownOpen} onOpenChange={setDropdownOpen}>
                        <DropdownMenu.Trigger>
                          <IconButton
                            variant="ghost"
                            size="2"
                            className="touch-target"
                            style={{ borderRadius: '50%' }}
                          >
                            <Avatar
                              size="2"
                              src={session.user.image || ''}
                              fallback={
                                session.user.name?.charAt(0) || session.user.email?.charAt(0) || 'U'
                              }
                            />
                          </IconButton>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content size="2" align="end">
                          <Box p="2">
                            <Flex direction="column" gap="1">
                              {session.user.name && (
                                <Text size="2" weight="medium">
                                  {session.user.name}
                                </Text>
                              )}
                              {session.user.email && (
                                <Text
                                  size="1"
                                  color="gray"
                                  style={{ maxWidth: '200px' }}
                                  className="truncate"
                                >
                                  {session.user.email}
                                </Text>
                              )}
                            </Flex>
                          </Box>
                          <Separator size="4" />
                          <DropdownMenu.Item asChild>
                            <Link href="/dashboard" onClick={handleNavigation}>
                              <Flex align="center" gap="2">
                                <User size={16} />
                                <Text size="2">Dashboard</Text>
                              </Flex>
                            </Link>
                          </DropdownMenu.Item>
                          <DropdownMenu.Item onClick={handleIDEPreferences}>
                            <Flex align="center" gap="2">
                              <Code size={16} />
                              <Text size="2">IDE Preferences</Text>
                            </Flex>
                          </DropdownMenu.Item>
                          <DropdownMenu.Item asChild>
                            <Link href="/settings" onClick={handleNavigation}>
                              <Flex align="center" gap="2">
                                <Settings size={16} />
                                <Text size="2">Settings</Text>
                              </Flex>
                            </Link>
                          </DropdownMenu.Item>
                          <Separator size="4" />
                          <DropdownMenu.Item onClick={handleSignOut}>
                            <Flex align="center" gap="2">
                              <LogOut size={16} />
                              <Text size="2">Sign Out</Text>
                            </Flex>
                          </DropdownMenu.Item>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    ) : (
                      <Button variant="ghost" size="2" asChild className="touch-target">
                        <Link href="/auth/signin" onClick={handleNavigation}>
                          <Text size="2">Sign In</Text>
                        </Link>
                      </Button>
                    )}
                  </Flex>
                </Box>

                {/* Mobile Menu Drawer - Show on small screens */}
                <Box display={{ sm: 'none', initial: 'block' }}>
                  <Drawer open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
                    <DrawerTrigger asChild>
                      <IconButton
                        ref={drawerTriggerRef}
                        variant="ghost"
                        size="3"
                        className="touch-target mobile-menu-button"
                        aria-label="Toggle mobile menu"
                        aria-expanded={mobileMenuOpen}
                        aria-controls="mobile-navigation-menu"
                        style={{ borderRadius: 'var(--radius-3)' }}
                      >
                        <Menu size={20} />
                      </IconButton>
                    </DrawerTrigger>
                    <DrawerContent className="max-h-[90vh]" id="mobile-navigation-menu">
                      <DrawerHeader>
                        <DrawerTitle>Menu</DrawerTitle>
                        <DrawerDescription>Navigate and manage your account</DrawerDescription>
                      </DrawerHeader>

                      <Box className="flex-1 overflow-y-auto px-6 py-4">
                        <Flex direction="column" gap="1">
                          {/* Primary Navigation Section */}
                          <Box className="mb-4">
                            <Text size="2" weight="medium" color="gray" className="mb-3 block px-3">
                              Navigation
                            </Text>
                            <Flex direction="column" gap="1">
                              {navigationLinks.map((link, index) => {
                                const icons = [Home, FileText, Code, Settings, FileText];
                                const IconComponent = icons[index] || FileText;

                                return (
                                  <Link key={link.href} href={link.href} onClick={handleNavigation}>
                                    <Button
                                      variant="ghost"
                                      size="3"
                                      className="drawer-menu-item h-12 w-full justify-start"
                                      style={{ borderRadius: 'var(--radius-3)' }}
                                      aria-label={`Navigate to ${link.label}`}
                                    >
                                      <Flex align="center" gap="3" className="w-full">
                                        <IconComponent
                                          size={20}
                                          className="text-[var(--gray-11)]"
                                          aria-hidden="true"
                                        />
                                        <Text size="3" weight="medium">
                                          {link.label}
                                        </Text>
                                      </Flex>
                                    </Button>
                                  </Link>
                                );
                              })}
                            </Flex>
                          </Box>

                          {/* Settings & Preferences Section */}
                          <Box className="mb-4">
                            <Text size="2" weight="medium" color="gray" className="mb-3 block px-3">
                              Preferences
                            </Text>
                            <Flex direction="column" gap="1">
                              {/* Theme Toggle */}
                              <Button
                                variant="ghost"
                                size="3"
                                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                                className="drawer-menu-item h-12 w-full justify-start"
                                style={{ borderRadius: 'var(--radius-3)' }}
                                aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`}
                              >
                                <Flex align="center" gap="3" className="w-full">
                                  <Box
                                    style={{ position: 'relative', width: '20px', height: '20px' }}
                                  >
                                    <Sun
                                      size={20}
                                      className="dark:-rotate-90 absolute top-0 left-0 rotate-0 scale-100 text-[var(--gray-11)] transition-all dark:scale-0"
                                      aria-hidden="true"
                                    />
                                    <Moon
                                      size={20}
                                      className="absolute top-0 left-0 rotate-90 scale-0 text-[var(--gray-11)] transition-all dark:rotate-0 dark:scale-100"
                                      aria-hidden="true"
                                    />
                                  </Box>
                                  <Text size="3" weight="medium">
                                    {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}
                                  </Text>
                                </Flex>
                              </Button>

                              {/* Language Switcher */}
                              <Box className="px-3 py-2">
                                <LanguageSwitcher currentLocale={locale} />
                              </Box>
                            </Flex>
                          </Box>

                          {/* Account Section */}
                          {session?.user ? (
                            <Box className="mb-4">
                              <Text
                                size="2"
                                weight="medium"
                                color="gray"
                                className="mb-3 block px-3"
                              >
                                Account
                              </Text>

                              {/* User Info Card */}
                              <Box
                                className="mb-3 rounded-lg border border-[var(--gray-4)] bg-[var(--gray-2)] p-4"
                                style={{ borderRadius: 'var(--radius-3)' }}
                              >
                                <Flex align="center" gap="3">
                                  <Avatar
                                    size="3"
                                    src={session.user.image || ''}
                                    fallback={
                                      session.user.name?.charAt(0) ||
                                      session.user.email?.charAt(0) ||
                                      'U'
                                    }
                                  />
                                  <Flex direction="column" gap="1" className="min-w-0 flex-1">
                                    {session.user.name && (
                                      <Text size="3" weight="medium" className="truncate">
                                        {session.user.name}
                                      </Text>
                                    )}
                                    {session.user.email && (
                                      <Text size="2" color="gray" className="truncate">
                                        {session.user.email}
                                      </Text>
                                    )}
                                  </Flex>
                                </Flex>
                              </Box>

                              <Flex direction="column" gap="1">
                                <Button
                                  variant="ghost"
                                  size="3"
                                  onClick={handleIDEPreferences}
                                  className="drawer-menu-item h-12 w-full justify-start"
                                  style={{ borderRadius: 'var(--radius-3)' }}
                                  aria-label="Open IDE preferences"
                                >
                                  <Flex align="center" gap="3">
                                    <Code size={20} className="text-[var(--gray-11)]" />
                                    <Text size="3" weight="medium">
                                      IDE Preferences
                                    </Text>
                                  </Flex>
                                </Button>

                                <Link href="/settings" onClick={handleNavigation}>
                                  <Button
                                    variant="ghost"
                                    size="3"
                                    className="drawer-menu-item h-12 w-full justify-start"
                                    style={{ borderRadius: 'var(--radius-3)' }}
                                    aria-label="Open settings"
                                  >
                                    <Flex align="center" gap="3">
                                      <Settings size={20} className="text-[var(--gray-11)]" />
                                      <Text size="3" weight="medium">
                                        Settings
                                      </Text>
                                    </Flex>
                                  </Button>
                                </Link>

                                <Button
                                  variant="ghost"
                                  size="3"
                                  onClick={handleSignOut}
                                  className="drawer-menu-item h-12 w-full justify-start"
                                  color="red"
                                  style={{ borderRadius: 'var(--radius-3)' }}
                                  aria-label="Sign out of your account"
                                >
                                  <Flex align="center" gap="3">
                                    <LogOut size={20} />
                                    <Text size="3" weight="medium">
                                      Sign Out
                                    </Text>
                                  </Flex>
                                </Button>
                              </Flex>
                            </Box>
                          ) : (
                            <Box className="mb-4">
                              <Text
                                size="2"
                                weight="medium"
                                color="gray"
                                className="mb-3 block px-3"
                              >
                                Account
                              </Text>
                              <Link href="/auth/signin" onClick={handleNavigation}>
                                <Button
                                  variant="surface"
                                  size="3"
                                  className="drawer-menu-item h-12 w-full justify-center"
                                  style={{ borderRadius: 'var(--radius-3)' }}
                                  aria-label="Sign in to your account"
                                >
                                  <Flex align="center" gap="3">
                                    <LogIn size={20} />
                                    <Text size="3" weight="medium">
                                      Sign In
                                    </Text>
                                  </Flex>
                                </Button>
                              </Link>
                            </Box>
                          )}

                          {/* External Links Section */}
                          <Box>
                            <Text size="2" weight="medium" color="gray" className="mb-3 block px-3">
                              Resources
                            </Text>
                            <Button
                              variant="ghost"
                              size="3"
                              asChild
                              className="drawer-menu-item h-12 w-full justify-start"
                              style={{ borderRadius: 'var(--radius-3)' }}
                              aria-label="Visit GitHub repository"
                            >
                              <Link
                                href="https://github.com/ranglang/onlyrules"
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <Flex align="center" gap="3">
                                  <GitHubIcon size={20} className="text-[var(--gray-11)]" />
                                  <Text size="3" weight="medium">
                                    GitHub
                                  </Text>
                                </Flex>
                              </Link>
                            </Button>
                          </Box>
                        </Flex>
                      </Box>

                      <DrawerFooter>
                        <DrawerClose asChild>
                          <Button
                            variant="soft"
                            size="3"
                            className="transition-all duration-200"
                            style={{ borderRadius: 'var(--radius-3)' }}
                          >
                            Close Menu
                          </Button>
                        </DrawerClose>
                      </DrawerFooter>
                    </DrawerContent>
                  </Drawer>
                </Box>
              </Flex>
            </Flex>
          </Container>
        </nav>
      </Box>

      {/* IDE Preferences Dialog */}
      <IDEPreferenceManager open={showIDEPreferences} onOpenChange={setShowIDEPreferences} />
    </>
  );
}
