import { Box, Container, Flex, Heading, Text } from '@radix-ui/themes';
import { Code, Github, Mail, MapPin } from 'lucide-react';
import Link from 'next/link';
import { FooterLink } from './footer-link';
import { FooterSection } from './footer-section';

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <Box asChild>
      {/** biome-ignore lint/a11y/useSemanticElements: <explanation> */}
      <footer
        role="contentinfo"
        aria-label="Site footer"
        style={{
          marginTop: 'auto',
          paddingTop: '4rem',
          borderTopWidth: '1px',
          borderTopStyle: 'solid',
          borderTopColor: 'var(--gray-6)',
          backgroundColor: 'var(--gray-2)',
        }}
      >
        <Container
          size="4"
          px={{ initial: '6', sm: '8', md: '10' }}
          py={{ initial: '8', md: '10' }}
        >
          {/* Single unified footer section */}
          <Flex direction="column" gap="8">
            {/* Main Content Row */}
            <Flex direction={{ initial: 'column', md: 'row' }} gap={{ initial: '8', md: '9' }}>
              {/* Company Information */}
              <Box style={{ maxWidth: '360px', flex: '1 1 auto' }}>
                <Link href="/" style={{ marginBottom: '16px', display: 'inline-block' }}>
                  <Flex align="center" gap="2">
                    <Code size={24} color="var(--accent-9)" />
                    <Heading size="6" weight="bold" color="gray" highContrast>
                      OnlyRules
                    </Heading>
                  </Flex>
                </Link>

                <Text
                  size="3"
                  color="gray"
                  mb="5"
                  style={{
                    lineHeight: '1.6',
                    maxWidth: '320px',
                  }}
                >
                  The ultimate AI prompt management platform for developers. Create, organize, and
                  share AI prompt rules for your favorite IDEs to boost coding productivity.
                </Text>

                {/* Contact Information */}
                <Flex direction="column" gap="3" mb="5">
                  <Flex align="center" gap="2">
                    <Mail size={16} color="var(--gray-9)" />
                    <FooterLink href="mailto:<EMAIL>">
                      <EMAIL>
                    </FooterLink>
                  </Flex>
                  <Flex align="center" gap="2">
                    <MapPin size={16} color="var(--gray-9)" />
                    <Text size="3" color="gray">
                      Global Remote Team
                    </Text>
                  </Flex>
                </Flex>

                {/* Social Links */}
                <Flex align="center" gap="3">
                  <FooterLink
                    href="https://github.com/ranglang/onlyrules"
                    external
                    variant="surface"
                  >
                    <Flex align="center" gap="2" px="3" py="2">
                      <Github size={18} />
                      <Text size="3" weight="medium">
                        GitHub
                      </Text>
                    </Flex>
                  </FooterLink>
                </Flex>
              </Box>

              {/* Navigation Sections */}
              <Flex
                direction={{ initial: 'column', sm: 'row' }}
                gap={{ initial: '6', sm: '8' }}
                style={{ flex: '2 1 auto' }}
                wrap="wrap"
              >
                {/* Product Section */}
                <FooterSection title="Product">
                  <FooterLink href="/templates">Ruleset Library</FooterLink>
                  <FooterLink href="/dashboard">User Dashboard</FooterLink>
                  <FooterLink href="/ides">IDE Integrations</FooterLink>
                  <FooterLink href="/rulesets">Rule Collections</FooterLink>
                </FooterSection>

                {/* Resources Section */}
                <FooterSection title="Resources">
                  <FooterLink href="/docs">Documentation</FooterLink>
                  <FooterLink href="/docs/api">API Reference</FooterLink>
                  <FooterLink href="/docs/guides">Setup Guides</FooterLink>
                  <FooterLink href="/docs/ides">IDE Setup</FooterLink>
                  <FooterLink href="/sitemap.xml" external>
                    Sitemap
                  </FooterLink>
                </FooterSection>

                {/* Community Section */}
                <FooterSection title="Community">
                  <FooterLink href="https://github.com/ranglang/onlyrules" external>
                    GitHub Repository
                  </FooterLink>
                  <FooterLink href="https://github.com/ranglang/onlyrules/issues" external>
                    Report Issues
                  </FooterLink>
                  <FooterLink href="https://github.com/ranglang/onlyrules/discussions" external>
                    Discussions
                  </FooterLink>
                  <FooterLink href="/contact">Contact Us</FooterLink>
                </FooterSection>

                {/* Legal Section */}
                <FooterSection title="Legal">
                  <FooterLink href="/privacy">Privacy Policy</FooterLink>
                  <FooterLink href="/terms">Terms of Service</FooterLink>
                  <FooterLink href="/docs/guides/seo-setup">SEO Guidelines</FooterLink>
                </FooterSection>
              </Flex>
            </Flex>

            {/* Copyright and Built with love - now integrated in the same section */}
            <Flex
              direction={{ initial: 'column', sm: 'row' }}
              align={{ initial: 'start', sm: 'center' }}
              justify="between"
              gap="3"
              pt="4"
              style={{
                borderTopWidth: '1px',
                borderTopStyle: 'solid',
                borderTopColor: 'var(--gray-5)',
              }}
            >
              <Text size="2" color="gray" weight="medium">
                © {currentYear} OnlyRules. All rights reserved.
              </Text>

              <Text size="2" color="gray" style={{ opacity: 0.8 }}>
                Built with ❤️ for developers
              </Text>
            </Flex>
          </Flex>

          {/* Structured Data for SEO */}
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                '@context': 'https://schema.org',
                '@type': 'Organization',
                name: 'OnlyRules',
                description: 'AI Prompt Management Platform for Developers',
                url: process.env.NEXT_PUBLIC_APP_URL || 'https://onlyrules.codes',
                logo: `${process.env.NEXT_PUBLIC_APP_URL || 'https://onlyrules.codes'}/logo.png`,
                sameAs: ['https://github.com/ranglang/onlyrules'],
                contactPoint: {
                  '@type': 'ContactPoint',
                  email: '<EMAIL>',
                  contactType: 'Customer Support',
                },
              }),
            }}
          />
        </Container>
      </footer>
    </Box>
  );
}
