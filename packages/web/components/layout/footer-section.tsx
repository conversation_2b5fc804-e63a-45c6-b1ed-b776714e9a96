import { Box, Flex, Heading } from '@radix-ui/themes';

interface FooterSectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

export function FooterSection({ title, children, className }: FooterSectionProps) {
  return (
    <Box className={className} style={{ minWidth: '160px', flex: '1 0 auto' }}>
      <Heading as="h3" size="4" weight="medium" color="gray" highContrast mb="4">
        {title}
      </Heading>
      <Flex direction="column" gap="3">
        {children}
      </Flex>
    </Box>
  );
}
