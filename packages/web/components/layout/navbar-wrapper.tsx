'use client';

import { usePathname } from 'next/navigation';
import { Suspense } from 'react';
import { ClientNavbar } from './client-navbar';
import { StaticNavbar } from './static-navbar';

// Fallback component for SSG
function NavbarFallback() {
  return <StaticNavbar />;
}

function NavbarContent() {
  const pathname = usePathname();

  // Don't render the main navbar for static pages - they handle their own
  if (pathname === '/') {
    return null;
  }

  return <ClientNavbarWithLocale />;
}

function ClientNavbarWithLocale() {
  // Since we can't use async in client components, we'll need to handle locale differently
  // For now, defaulting to 'en' and can be improved later with a context
  return <ClientNavbar locale="en" />;
}

export function NavbarWrapper() {
  return (
    <Suspense fallback={<NavbarFallback />}>
      <NavbarContent />
    </Suspense>
  );
}
