'use client';

import { Badge, <PERSON>, Button, Flex, ScrollArea, Text } from '@radix-ui/themes';
import { FileText, List } from 'lucide-react';
import type { RulesetRule } from '@/hooks/use-ruleset-queries';
import { cn } from '@/lib/utils';

interface RulesetRuleNavigationProps {
  rules: RulesetRule[];
  selectedRuleId: string | 'ALL';
  onRuleSelect: (ruleId: string | 'ALL') => void;
}

export function RulesetRuleNavigation({
  rules,
  selectedRuleId,
  onRuleSelect,
}: RulesetRuleNavigationProps) {
  return (
    <Box className="h-full bg-gray-50 md:border-r dark:bg-gray-900">
      <Box p="4" className="border-b">
        <Text size="3" weight="medium" className="flex items-center gap-2">
          <List className="h-4 w-4" />
          Navigation
        </Text>
      </Box>

      <ScrollArea className="h-[calc(100%-60px)] max-h-[300px] md:h-[calc(100%-60px)] md:max-h-none">
        <Box p="2">
          {/* ALL Tab */}
          <Button
            variant={selectedRuleId === 'ALL' ? 'solid' : 'ghost'}
            size="2"
            className={cn(
              'mb-2 h-auto w-full justify-start p-3',
              selectedRuleId === 'ALL'
                ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                : 'hover:bg-gray-100 dark:hover:bg-gray-800'
            )}
            onClick={() => onRuleSelect('ALL')}
          >
            <Flex direction="column" align="start" gap="1" className="w-full">
              <Flex align="center" gap="2">
                <FileText className="h-4 w-4" />
                <Text size="2" weight="medium">
                  ALL RULES
                </Text>
              </Flex>
              <Text size="1" color="gray">
                View all {rules.length} rules combined
              </Text>
            </Flex>
          </Button>

          {/* Individual Rules */}
          <Box className="space-y-1">
            {rules.map(({ rule }, index) => (
              <Button
                key={rule.id}
                variant={selectedRuleId === rule.id ? 'solid' : 'ghost'}
                size="2"
                className={cn(
                  'h-auto w-full justify-start p-3',
                  selectedRuleId === rule.id
                    ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                )}
                onClick={() => onRuleSelect(rule.id)}
              >
                <Flex direction="column" align="start" gap="1" className="w-full">
                  <Flex align="center" gap="2" className="w-full">
                    <Text size="1" color="gray" className="min-w-[20px]">
                      {index + 1}.
                    </Text>
                    <Text size="2" weight="medium" className="flex-1 truncate">
                      {rule.title}
                    </Text>
                  </Flex>

                  {rule.description && (
                    <Text size="1" color="gray" className="line-clamp-2 pl-6 text-left">
                      {rule.description}
                    </Text>
                  )}

                  {rule.tags.length > 0 && (
                    <Flex gap="1" wrap="wrap" className="pl-6">
                      {rule.tags.slice(0, 2).map(({ tag }) => (
                        <Badge
                          key={tag.id}
                          variant="soft"
                          size="1"
                          style={{
                            backgroundColor: `${tag.color}20`,
                            color: tag.color,
                            fontSize: '10px',
                          }}
                        >
                          {tag.name}
                        </Badge>
                      ))}
                      {rule.tags.length > 2 && (
                        <Badge variant="soft" size="1" style={{ fontSize: '10px' }}>
                          +{rule.tags.length - 2}
                        </Badge>
                      )}
                    </Flex>
                  )}
                </Flex>
              </Button>
            ))}
          </Box>
        </Box>
      </ScrollArea>
    </Box>
  );
}
