'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Flex, Text } from '@radix-ui/themes';

/**
 * Demo component showing how to use Tailwind CSS with Radix UI Themes
 * This demonstrates the resolved conflict between the two systems
 */
export function TailwindRadixDemo() {
  return (
    <div className="space-y-6 p-6">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-4 font-bold text-3xl text-foreground">
          Tailwind CSS + Radix UI Themes Integration
        </h1>

        <p className="mb-8 text-muted-foreground">
          This demo shows how Tailwind CSS utilities work seamlessly with Radix UI Themes
          components.
        </p>

        {/* Grid layout using Tailwind */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Card 1: Pure Radix UI */}
          <Card className="p-4">
            <Flex direction="column" gap="3">
              <Text size="4" weight="bold">
                Pure Radix UI
              </Text>
              <Text size="2" color="gray">
                This card uses only Radix UI components with their default styling.
              </Text>
              <Button>Radix Button</Button>
            </Flex>
          </Card>

          {/* Card 2: Tailwind + Radix UI */}
          <Card className="border border-border bg-card p-4">
            <Flex direction="column" gap="3">
              <Text size="4" weight="bold" className="text-foreground">
                Tailwind + Radix UI
              </Text>
              <Text size="2" className="text-muted-foreground">
                This card combines Tailwind utilities with Radix components.
              </Text>
              <Button className="bg-primary text-primary-foreground hover:bg-primary/90">
                Styled Button
              </Button>
            </Flex>
          </Card>

          {/* Card 3: Advanced Integration */}
          <Card className="border border-border bg-gradient-to-br from-primary/10 to-accent/10 p-4">
            <Flex direction="column" gap="3">
              <Flex align="center" justify="between">
                <Text size="4" weight="bold" className="text-foreground">
                  Advanced
                </Text>
                <Badge color="blue" className="radix-theme-aware">
                  New
                </Badge>
              </Flex>
              <Text size="2" className="text-muted-foreground">
                This shows advanced integration with gradients and custom utilities.
              </Text>
              <Button className="bg-accent text-accent-foreground transition-colors hover:bg-accent/90">
                Gradient Card
              </Button>
            </Flex>
          </Card>
        </div>

        {/* Responsive layout demo */}
        <div className="mt-12 rounded-lg bg-muted p-6">
          <h2 className="mb-4 font-semibold text-2xl text-foreground">Responsive Layout Demo</h2>

          <Flex
            direction={{ initial: 'column', sm: 'row' }}
            gap="4"
            align={{ initial: 'start', sm: 'center' }}
          >
            <Box style={{ flex: 1 }}>
              <Text className="text-muted-foreground">
                This layout adapts to different screen sizes using Tailwind's responsive utilities
                while maintaining Radix UI's consistent theming.
              </Text>
            </Box>

            <Flex gap="2" wrap="wrap">
              <Button size="2" variant="soft">
                Small
              </Button>
              <Button size="3" variant="solid" className="bg-primary">
                Medium
              </Button>
              <Button size="4" variant="outline" className="border-border">
                Large
              </Button>
            </Flex>
          </Flex>
        </div>

        {/* Color system demo */}
        <div className="mt-8 grid grid-cols-2 gap-4 md:grid-cols-4">
          <div className="rounded-lg border border-border bg-background p-4">
            <div className="mb-2 h-8 w-full rounded bg-primary"></div>
            <Text size="1" className="text-muted-foreground">
              Primary
            </Text>
          </div>

          <div className="rounded-lg border border-border bg-background p-4">
            <div className="mb-2 h-8 w-full rounded bg-secondary"></div>
            <Text size="1" className="text-muted-foreground">
              Secondary
            </Text>
          </div>

          <div className="rounded-lg border border-border bg-background p-4">
            <div className="mb-2 h-8 w-full rounded bg-accent"></div>
            <Text size="1" className="text-muted-foreground">
              Accent
            </Text>
          </div>

          <div className="rounded-lg border border-border bg-background p-4">
            <div className="mb-2 h-8 w-full rounded bg-muted"></div>
            <Text size="1" className="text-muted-foreground">
              Muted
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
}
