'use client';

import { But<PERSON>, <PERSON> } from '@radix-ui/themes';
import { FileText, Plus } from 'lucide-react';
import { useState } from 'react';
import { createEmptySection, duplicateSection, moveSectionInArray } from '@/lib/rule-sections';
import type { RuleSection } from '@/lib/store';
import { RuleSectionEditor } from './rule-section-editor';

interface RuleSectionsListProps {
  sections: RuleSection[];
  onSectionsChange: (sections: RuleSection[]) => void;
}

export function RuleSectionsList({ sections, onSectionsChange }: RuleSectionsListProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(sections.length === 1 ? [sections[0]?.id] : [])
  );

  const handleSectionUpdate = (index: number, updatedSection: RuleSection) => {
    const newSections = [...sections];
    newSections[index] = updatedSection;
    onSectionsChange(newSections);
  };

  const handleAddSection = () => {
    const newSection = createEmptySection(sections.length);
    const newSections = [...sections, newSection];
    onSectionsChange(newSections);

    // Expand the new section
    setExpandedSections((prev) => new Set([...prev, newSection.id]));
  };

  const handleDeleteSection = (index: number) => {
    if (sections.length === 1) return; // Don't allow deleting the last section

    const sectionToDelete = sections[index];
    const newSections = sections.filter((_, i) => i !== index);
    onSectionsChange(newSections);

    // Remove from expanded sections
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      newSet.delete(sectionToDelete.id);
      return newSet;
    });
  };

  const handleDuplicateSection = (index: number) => {
    const sectionToDuplicate = sections[index];
    const duplicatedSection = duplicateSection(sectionToDuplicate);
    const newSections = [...sections];
    newSections.splice(index + 1, 0, duplicatedSection);
    onSectionsChange(newSections);

    // Expand the duplicated section
    setExpandedSections((prev) => new Set([...prev, duplicatedSection.id]));
  };

  const handleMoveSection = (fromIndex: number, toIndex: number) => {
    const newSections = moveSectionInArray(sections, fromIndex, toIndex);
    onSectionsChange(newSections);
  };

  const handleToggleExpanded = (sectionId: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  const handleExpandAll = () => {
    setExpandedSections(new Set([...sections.map((s) => s.id)]));
  };

  const handleCollapseAll = () => {
    setExpandedSections(new Set([]));
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Text className="font-medium text-base">Rule Sections</Text>
          <span className="text-muted-foreground text-sm">
            ({sections.length} section{sections.length !== 1 ? 's' : ''})
          </span>
        </div>

        <div className="flex items-center gap-2">
          {sections.length > 1 && (
            <>
              <Button variant="ghost" size="1" onClick={handleExpandAll} className="text-xs">
                Expand All
              </Button>
              <Button variant="ghost" size="1" onClick={handleCollapseAll} className="text-xs">
                Collapse All
              </Button>
            </>
          )}

          <Button variant="outline" size="1" onClick={handleAddSection} className="gap-2">
            <Plus className="h-4 w-4" />
            Add Section
          </Button>
        </div>
      </div>

      {/* Sections List */}
      <div className="space-y-3">
        {sections.length === 0 ? (
          <div className="rounded-lg border-2 border-muted border-dashed py-12 text-center">
            <FileText className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
            <h3 className="mb-2 font-semibold text-lg">No sections yet</h3>
            <p className="mb-4 text-muted-foreground">Add your first section to get started</p>
            <Button onClick={handleAddSection} className="gap-2">
              <Plus className="h-4 w-4" />
              Add Section
            </Button>
          </div>
        ) : (
          sections.map((section, index) => (
            <RuleSectionEditor
              key={section.id}
              section={section}
              index={index}
              totalSections={sections.length}
              isExpanded={expandedSections.has(section.id)}
              onUpdate={(updatedSection) => handleSectionUpdate(index, updatedSection)}
              onDelete={() => handleDeleteSection(index)}
              onDuplicate={() => handleDuplicateSection(index)}
              onMoveUp={() => handleMoveSection(index, index - 1)}
              onMoveDown={() => handleMoveSection(index, index + 1)}
              onToggleExpanded={() => handleToggleExpanded(section.id)}
            />
          ))
        )}
      </div>

      {/* Help Text */}
      {sections.length > 0 && (
        <div className="rounded-lg bg-muted/50 p-3 text-muted-foreground text-sm">
          <p className="mb-1 font-medium">Section Format:</p>
          <ul className="space-y-1 text-xs">
            <li>
              • Sections are separated by <code>---</code> delimiters
            </li>
            <li>
              • Optional metadata: <code>description</code>, <code>name</code>, <code>globs</code>
            </li>
            <li>• Content supports markdown formatting</li>
            <li>• Use file patterns (globs) to target specific file types</li>
          </ul>
        </div>
      )}
    </div>
  );
}
