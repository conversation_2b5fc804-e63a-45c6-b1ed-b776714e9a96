'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, DropdownMenu } from '@radix-ui/themes';
import { formatDistanceToNow } from 'date-fns';
import { Clock, Download, Eye, FileJson, FileText, Lock } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

interface Rule {
  id: string;
  title: string;
  description: string | null;
  content: string;
  visibility: 'PUBLIC' | 'PRIVATE';
  user: {
    id: string;
    name: string | null;
    email: string;
  };
  tags: Array<{
    tag: {
      id: string;
      name: string;
      color: string;
    };
  }>;
}

interface RulesetRule {
  order: number;
  rule: Rule;
}

interface Ruleset {
  id: string;
  name: string;
  description: string | null;
  visibility: 'PUBLIC' | 'PRIVATE';
  shareToken: string | null;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string | null;
    email: string;
  };
  rules: RulesetRule[];
}

interface RulesetCardProps {
  ruleset: Ruleset;
  onEdit?: (ruleset: Ruleset) => void;
  onDelete?: (ruleset: Ruleset) => void;
  showActions?: boolean;
}

export function RulesetCard({ ruleset, onEdit, onDelete, showActions = false }: RulesetCardProps) {
  const isPublic = ruleset.visibility === 'PUBLIC';
  const ruleCount = ruleset.rules.length;

  // Get all tags from rules
  const allTags = ruleset.rules.flatMap((r) => r.rule.tags.map((t) => t.tag));
  const uniqueTags = allTags.filter(
    (tag, index, self) => index === self.findIndex((t) => t.id === tag.id)
  );

  const handleDownloadJSON = () => {
    const data = {
      name: ruleset.name,
      description: ruleset.description,
      rules: ruleset.rules.map((rulesetRule) => ({
        order: rulesetRule.order,
        title: rulesetRule.rule.title,
        description: rulesetRule.rule.description,
        content: rulesetRule.rule.content,
        tags: rulesetRule.rule.tags.map((t) => t.tag.name),
      })),
      tags: uniqueTags.map((t) => t.name),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json',
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${ruleset.name.toLowerCase().replace(/\s+/g, '-')}-ruleset.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Ruleset downloaded as JSON');
  };

  const handleDownloadMDX = async () => {
    // Only download MDX for public rulesets via API
    if (ruleset.visibility !== 'PUBLIC') {
      toast.error('Only public rulesets can be downloaded as MDX');
      return;
    }

    try {
      const response = await fetch(`/api/rulesets/download?id=${ruleset.id}`);
      if (!response.ok) {
        throw new Error('Failed to download ruleset');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${ruleset.name.toLowerCase().replace(/\s+/g, '-')}-ruleset.mdx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Ruleset downloaded as MDX');
    } catch (_error) {
      toast.error('Failed to download ruleset as MDX');
    }
  };

  return (
    <Card className="flex h-full flex-col space-y-4 p-4">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="mb-2 font-semibold text-lg">{ruleset.name}</h3>
          {ruleset.description && (
            <p className="mb-2 line-clamp-2 text-gray-600 text-sm">{ruleset.description}</p>
          )}
        </div>
        <div className="ml-4 flex items-center gap-2">
          {isPublic ? (
            <Badge color="green" variant="soft">
              <Eye className="mr-1 h-3 w-3" />
              Public
            </Badge>
          ) : (
            <Badge color="gray" variant="soft">
              <Lock className="mr-1 h-3 w-3" />
              Private
            </Badge>
          )}
        </div>
      </div>

      {/* Rule count */}
      <div className="flex items-center text-gray-600 text-sm">
        <div className="flex items-center gap-1">
          <FileText className="h-4 w-4" />
          <span>
            {ruleCount} rule{ruleCount !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      {/* Tags */}
      {uniqueTags.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {uniqueTags.slice(0, 5).map((tag) => (
            <Badge
              key={tag.id}
              variant="soft"
              className="text-xs"
              style={{ backgroundColor: `${tag.color}20`, color: tag.color }}
            >
              {tag.name}
            </Badge>
          ))}
          {uniqueTags.length > 5 && (
            <Badge variant="soft" className="text-xs">
              +{uniqueTags.length - 5}
            </Badge>
          )}
        </div>
      )}

      {/* Author and timestamp */}
      <div className="flex items-center justify-between text-gray-600 text-sm">
        <span>{ruleset.user.name || ruleset.user.email}</span>
        <div className="flex items-center gap-1 text-xs">
          <Clock className="h-3 w-3" />
          <span>
            Updated {formatDistanceToNow(new Date(ruleset.updatedAt), { addSuffix: true })}
          </span>
        </div>
      </div>

      {/* Actions */}
      <div className="mt-auto flex w-full gap-2">
        <Button asChild variant="solid" className="flex-1">
          <Link
            href={`/rulesets/${ruleset.id}${isPublic && ruleset.shareToken ? `?token=${ruleset.shareToken}` : ''}`}
          >
            View Ruleset
          </Link>
        </Button>

        {/* Download dropdown for public rulesets */}
        {isPublic && (
          <DropdownMenu.Root>
            <DropdownMenu.Trigger>
              <Button variant="outline" size="2">
                <Download className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item onClick={handleDownloadJSON}>
                <FileJson className="mr-2 h-4 w-4" />
                JSON
              </DropdownMenu.Item>
              <DropdownMenu.Item onClick={handleDownloadMDX}>
                <FileText className="mr-2 h-4 w-4" />
                MDX
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
        )}

        {showActions && (
          <>
            {onEdit && (
              <Button variant="outline" onClick={() => onEdit(ruleset)}>
                Edit
              </Button>
            )}
            {onDelete && (
              <Button variant="outline" color="red" onClick={() => onDelete(ruleset)}>
                Delete
              </Button>
            )}
          </>
        )}
      </div>
    </Card>
  );
}
