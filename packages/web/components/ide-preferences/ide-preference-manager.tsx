'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alog, Select } from '@radix-ui/themes';
import { useAtom } from 'jotai';
import { Code, Plus, Settings, Star, StarOff, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { IDE_METADATA, type IDEPreference, type IDEType, idePreferencesAtom } from '@/lib/store';

interface IDEPreferenceManagerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function IDEPreferenceManager({ open, onOpenChange }: IDEPreferenceManagerProps) {
  const [preferences, setPreferences] = useAtom(idePreferencesAtom);
  const [selectedIDE, setSelectedIDE] = useState<IDEType | ''>('');

  const availableIDEs = Object.entries(IDE_METADATA).filter(
    ([type]) => !preferences.preferredIDEs.some((pref) => pref.type === type)
  );

  const handleAddIDE = () => {
    if (!selectedIDE) return;

    const newIDE: IDEPreference = {
      id: `${selectedIDE}-${Date.now()}`,
      name: IDE_METADATA[selectedIDE].name,
      type: selectedIDE,
      isDefault: preferences.preferredIDEs.length === 0, // First IDE becomes default
      addedAt: new Date().toISOString(),
    };

    const updatedPreferences = {
      ...preferences,
      preferredIDEs: [...preferences.preferredIDEs, newIDE],
    };

    setPreferences(updatedPreferences);
    setSelectedIDE('');
    toast.success(`${IDE_METADATA[selectedIDE].name} added to preferences`);
  };

  const handleRemoveIDE = (ideId: string) => {
    const ideToRemove = preferences.preferredIDEs.find((ide) => ide.id === ideId);
    const updatedIDEs = preferences.preferredIDEs.filter((ide) => ide.id !== ideId);

    // If removing the default IDE, set the first remaining IDE as default
    if (ideToRemove?.isDefault && updatedIDEs.length > 0) {
      updatedIDEs[0].isDefault = true;
    }

    setPreferences({
      preferredIDEs: updatedIDEs,
    });

    if (ideToRemove) {
      toast.success(`${ideToRemove.name} removed from preferences`);
    }
  };

  const handleSetDefault = (ideId: string) => {
    const ide = preferences.preferredIDEs.find((ide) => ide.id === ideId);
    const updatedIDEs = preferences.preferredIDEs.map((ide) => ({
      ...ide,
      isDefault: ide.id === ideId,
    }));

    setPreferences({
      ...preferences,
      preferredIDEs: updatedIDEs,
    });

    if (ide) {
      toast.success(`${ide.name} set as default IDE`);
    }
  };

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Content className="max-w-2xl">
        <Dialog.Title className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          IDE Preferences
        </Dialog.Title>
        <Dialog.Description>
          Manage your preferred IDEs for quick command generation. Set a default IDE and add your
          favorites.
        </Dialog.Description>

        <div className="space-y-6">
          {/* Add New IDE Section */}
          <div className="space-y-3">
            <h3 className="font-medium text-sm">Add IDE</h3>
            <div className="flex gap-2">
              <Select.Root
                value={selectedIDE}
                onValueChange={(value) => setSelectedIDE(value as IDEType | '')}
              >
                <Select.Trigger className="flex-1" placeholder="Select an IDE to add..." />
                <Select.Content>
                  {availableIDEs.map(([type, metadata]) => (
                    <Select.Item key={type} value={type}>
                      <div className="flex items-center gap-2">
                        <Code className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{metadata.name}</div>
                          <div className="text-muted-foreground text-xs">
                            {metadata.description}
                          </div>
                        </div>
                      </div>
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select.Root>
              <Button onClick={handleAddIDE} disabled={!selectedIDE} size="2">
                <Plus className="mr-1 h-4 w-4" />
                Add
              </Button>
            </div>
          </div>

          {/* Preferred IDEs List */}
          <div className="space-y-3">
            <h3 className="font-medium text-sm">
              Preferred IDEs ({preferences.preferredIDEs.length})
            </h3>

            {preferences.preferredIDEs.length === 0 ? (
              <div className="py-8 text-center text-muted-foreground">
                <Code className="mx-auto mb-2 h-8 w-8 opacity-50" />
                <p>No preferred IDEs added yet</p>
                <p className="text-xs">Add your favorite IDEs to generate quick commands</p>
              </div>
            ) : (
              <div className="space-y-2">
                {preferences.preferredIDEs.map((ide) => (
                  <div
                    key={ide.id}
                    className="flex items-center justify-between rounded-lg border p-3"
                  >
                    <div className="flex items-center gap-3">
                      <Code className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{ide.name}</span>
                          {ide.isDefault && (
                            <Badge variant="soft" className="text-xs">
                              Default
                            </Badge>
                          )}
                        </div>
                        <div className="text-muted-foreground text-xs">
                          {IDE_METADATA[ide.type].description}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="2"
                        onClick={() => handleSetDefault(ide.id)}
                        disabled={ide.isDefault}
                        className="h-8 w-8 p-0"
                      >
                        {ide.isDefault ? (
                          <Star className="h-4 w-4 fill-current" />
                        ) : (
                          <StarOff className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="2"
                        onClick={() => handleRemoveIDE(ide.id)}
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Help Text */}
          <div className="rounded-lg bg-muted p-3 text-muted-foreground text-xs">
            <p className="mb-1 font-medium">How it works:</p>
            <ul className="space-y-1">
              <li>• Add your favorite IDEs to generate quick npx commands</li>
              <li>• Set a default IDE for one-click command copying</li>
              <li>• Commands will include the --target flag for your selected IDE</li>
            </ul>
          </div>
        </div>
      </Dialog.Content>
    </Dialog.Root>
  );
}
