'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Dialog, DropdownMenu, Flex } from '@radix-ui/themes';
import { useAtom } from 'jotai';
import {
  Code,
  Copy,
  Download,
  Edit,
  ExternalLink,
  Eye,
  FileJson,
  FileText,
  MoreHorizontal,
  Share,
  Star,
  Terminal,
  Trash,
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { toast } from 'sonner';
import { CodeEditor } from '@/components/ui/code-editor';
import {
  copyToClipboard,
  generateAllIDECommands,
  generateBasicCommand,
  generateIDECommand,
  sortIDEPreferences,
} from '@/lib/ide-utils';
import { generateMDCFromRule } from '@/lib/mdc-utils';
import { APPLY_TYPE_METADATA, idePreferencesAtom, type Rule } from '@/lib/store';

/**
 * Copy Rule as MDC formatted text to clipboard
 */
export async function copyRuleToClipboard(rule: Rule): Promise<boolean> {
  try {
    const mdcText = generateMDCFromRule(rule);
    await navigator.clipboard.writeText(mdcText);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
}
interface RuleCardProps {
  rule: Rule;
  onEdit?: (rule: Rule) => void;
  onDelete?: (ruleId: string) => void;
  isOwner?: boolean;
}

export function RuleCard({ rule, onEdit, onDelete, isOwner = false }: RuleCardProps) {
  const [showContent, setShowContent] = useState(false);
  const [_copiedCommand, setCopiedCommand] = useState(false);
  const [idePreferences] = useAtom(idePreferencesAtom);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(rule.content);
    toast.success('Rule content copied to clipboard');
  };

  const handleCopyCommand = async () => {
    // Only available for public rules
    if (rule.visibility !== 'PUBLIC') {
      toast.error('Only public rules can be used with npx command');
      return;
    }

    const rawDataUrl = `${window.location.origin}/api/rules/raw?id=${rule.id}`;
    const command = generateBasicCommand(rawDataUrl);

    const success = await copyToClipboard(command);
    if (success) {
      setCopiedCommand(true);
      toast.success('CLI command copied to clipboard');

      // Reset the copied state after 2 seconds
      setTimeout(() => setCopiedCommand(false), 2000);
    } else {
      toast.error('Failed to copy command to clipboard');
    }
  };

  const handleCopyIDECommand = async (ideId: string) => {
    // Only available for public rules
    if (rule.visibility !== 'PUBLIC') {
      toast.error('Only public rules can be used with npx command');
      return;
    }

    const ide = idePreferences.preferredIDEs.find((ide) => ide.id === ideId);
    if (!ide) return;

    const rawDataUrl = `${window.location.origin}/api/rules/raw?id=${rule.id}`;
    const command = generateIDECommand(rawDataUrl, [ide.type], 'append');

    const success = await copyToClipboard(command);
    if (success) {
      toast.success(`${ide.name} command copied to clipboard`);
    } else {
      toast.error('Failed to copy command to clipboard');
    }
  };

  const handleCopyAppendDefaultIDECommand = async () => {
    // Only available for public rules
    if (rule.visibility !== 'PUBLIC') {
      toast.error('Only public rules can be used with npx command');
      return;
    }

    // const defaultIDE = idePreferences.preferredIDEs.find(ide => ide.isDefault);
    // if (!defaultIDE) {
    //   // Fallback to basic command if no default IDE
    //   return handleCopyCommand();
    // }

    const rawDataUrl = `${window.location.origin}/api/rules/raw?id=${rule.id}`;
    const command = generateAllIDECommands(rawDataUrl, idePreferences.preferredIDEs, 'append');

    const success = await copyToClipboard(command);
    if (success) {
      setCopiedCommand(true);
      toast.success(`command copied to clipboard`);

      // Reset the copied state after 2 seconds
      setTimeout(() => setCopiedCommand(false), 2000);
    } else {
      toast.error('Failed to copy command to clipboard');
    }
  };

  const handleCopyDefaultIDECommand = async () => {
    // Only available for public rules
    if (rule.visibility !== 'PUBLIC') {
      toast.error('Only public rules can be used with npx command');
      return;
    }

    // const defaultIDE = idePreferences.preferredIDEs.find(ide => ide.isDefault);
    // if (!defaultIDE) {
    //   // Fallback to basic command if no default IDE
    //   return handleCopyCommand();
    // }

    const rawDataUrl = `${window.location.origin}/api/rules/raw?id=${rule.id}`;
    const command = generateAllIDECommands(rawDataUrl, idePreferences.preferredIDEs, 'init');

    const success = await copyToClipboard(command);
    if (success) {
      setCopiedCommand(true);
      toast.success(`command copied to clipboard`);

      // Reset the copied state after 2 seconds
      setTimeout(() => setCopiedCommand(false), 2000);
    } else {
      toast.error('Failed to copy command to clipboard');
    }
  };

  const handleShare = async () => {
    if (rule.visibility === 'PUBLIC') {
      const shareUrl = `${window.location.origin}/rules/${rule.id}`;
      await navigator.clipboard.writeText(shareUrl);
      toast.success('Share link copied to clipboard');
    }
  };

  const handleDownloadJSON = () => {
    const data = {
      title: rule.title,
      description: rule.description,
      content: rule.content,
      tags: rule.tags.map((t) => t.tag.name),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json',
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${rule.title.toLowerCase().replace(/\s+/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Rule downloaded as JSON');
  };

  const handleDownloadMDX = async () => {
    // Only download MDX for public rules via API
    if (rule.visibility !== 'PUBLIC') {
      toast.error('Only public rules can be downloaded as MDX');
      return;
    }

    try {
      const response = await fetch(`/api/rules/download?id=${rule.id}`);
      if (!response.ok) {
        throw new Error('Failed to download rule');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${rule.title.toLowerCase().replace(/\s+/g, '-')}.mdx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Rule downloaded as MDX');
    } catch (_error) {
      toast.error('Failed to download rule as MDX');
    }
  };

  return (
    <>
      <Card className="group mobile-card transition-all duration-200 hover:shadow-md">
        <div className="pb-3">
          <Flex align="start" justify="between" gap="3">
            <Box className="space-y-2" style={{ flex: 1, minWidth: 0 }}>
              {rule.visibility === 'PUBLIC' ? (
                <Link href={`/rules/${rule.id}`} className="touch-target">
                  <Flex
                    align="center"
                    gap="1"
                    display="inline-flex"
                    className="cursor-pointer font-semibold text-base xs:text-lg transition-colors hover:text-primary"
                  >
                    <span className="truncate">{rule.title}</span>
                    <ExternalLink
                      className="h-3 w-3 opacity-0 group-hover:opacity-50"
                      style={{ flexShrink: 0 }}
                    />
                  </Flex>
                </Link>
              ) : (
                <button
                  type="button"
                  className="touch-target cursor-pointer truncate text-left font-semibold text-base xs:text-lg transition-colors hover:text-primary"
                  onClick={() => setShowContent(true)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      setShowContent(true);
                    }
                  }}
                >
                  {rule.title}
                </button>
              )}
              {rule.description && (
                <p className="line-clamp-2 text-muted-foreground text-xs xs:text-sm leading-relaxed">
                  {rule.description}
                </p>
              )}
            </Box>
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                <Button
                  variant="ghost"
                  size="1"
                  className="touch-target opacity-60 xs:opacity-0 transition-opacity xs:group-hover:opacity-100"
                  style={{ flexShrink: 0 }}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content align="end">
                <DropdownMenu.Item onClick={() => setShowContent(true)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View
                </DropdownMenu.Item>
                {rule.visibility === 'PUBLIC' && (
                  <DropdownMenu.Item asChild>
                    <Link href={`/rules/${rule.id}`}>
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Open Rule Page
                    </Link>
                  </DropdownMenu.Item>
                )}
                <DropdownMenu.Item onClick={handleCopy}>
                  <Copy className="mr-2 h-4 w-4" />
                  Copy Content
                </DropdownMenu.Item>
                <DropdownMenu.Item
                  onClick={async () => {
                    const success = await copyRuleToClipboard(rule);
                    if (success) {
                      toast.success('Rule copied as MDC format to clipboard');
                    } else {
                      toast.error('Failed to copy rule to clipboard');
                    }
                  }}
                >
                  <FileText className="mr-2 h-4 w-4" />
                  Copy as MDC
                </DropdownMenu.Item>

                {/* CLI Commands Submenu */}
                <DropdownMenu.Sub>
                  <DropdownMenu.SubTrigger>
                    <Terminal className="mr-2 h-4 w-4" />
                    Copy CLI Command
                  </DropdownMenu.SubTrigger>
                  <DropdownMenu.SubContent>
                    <DropdownMenu.Item onClick={handleCopyDefaultIDECommand}>
                      <Star className="mr-2 h-4 w-4 fill-current" />
                      Copy (init Mode)
                    </DropdownMenu.Item>
                    <DropdownMenu.Separator />

                    <DropdownMenu.Item onClick={handleCopyAppendDefaultIDECommand}>
                      <Star className="mr-2 h-4 w-4 fill-current" />
                      Copy (Append Mode)
                    </DropdownMenu.Item>
                    <DropdownMenu.Separator />

                    {/* Basic Command */}
                    <DropdownMenu.Item onClick={handleCopyCommand}>
                      <Terminal className="mr-2 h-4 w-4" />
                      Seprarat Command
                    </DropdownMenu.Item>

                    <DropdownMenu.Separator />
                    {sortIDEPreferences(idePreferences.preferredIDEs)
                      .filter((ide) => !ide.isDefault) // Don't duplicate default
                      .map((ide) => (
                        <DropdownMenu.Item
                          key={ide.id}
                          onClick={() => handleCopyIDECommand(ide.id)}
                        >
                          <Code className="mr-2 h-4 w-4" />
                          {ide.name}
                        </DropdownMenu.Item>
                      ))}

                    {/* No preferences message */}
                    {idePreferences.preferredIDEs.length === 0 && (
                      <div className="px-2 py-1.5 text-muted-foreground text-xs">
                        Add IDE preferences in settings for quick commands
                      </div>
                    )}
                  </DropdownMenu.SubContent>
                </DropdownMenu.Sub>

                <DropdownMenu.Item onClick={handleShare}>
                  <Share className="mr-2 h-4 w-4" />
                  Share
                </DropdownMenu.Item>

                <DropdownMenu.Sub>
                  <DropdownMenu.SubTrigger>
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </DropdownMenu.SubTrigger>
                  <DropdownMenu.SubContent>
                    <DropdownMenu.Item onClick={handleDownloadJSON}>
                      <FileJson className="mr-2 h-4 w-4" />
                      JSON
                    </DropdownMenu.Item>
                    <DropdownMenu.Item onClick={handleDownloadMDX}>
                      <FileText className="mr-2 h-4 w-4" />
                      MDX
                    </DropdownMenu.Item>
                  </DropdownMenu.SubContent>
                </DropdownMenu.Sub>
                {isOwner && onEdit && (
                  <>
                    <DropdownMenu.Separator />
                    <DropdownMenu.Item onClick={() => onEdit(rule)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenu.Item>
                  </>
                )}
                {isOwner && onDelete && (
                  <DropdownMenu.Item onClick={() => onDelete(rule.id)} className="text-destructive">
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenu.Item>
                )}
              </DropdownMenu.Content>
            </DropdownMenu.Root>
          </Flex>
        </div>

        <div className="space-y-3 xs:space-y-4">
          <Flex align="center" gap="2" wrap="wrap">
            <Badge
              variant="soft"
              color={(APPLY_TYPE_METADATA[rule.applyType]?.color as string) || 'gray'}
              className="text-xs xs:text-sm"
            >
              {APPLY_TYPE_METADATA[rule.applyType]?.name || rule.applyType}
            </Badge>
            {rule.visibility === 'PUBLIC' && (
              <Badge variant="outline" className="text-xs xs:text-sm">
                Public
              </Badge>
            )}
          </Flex>

          {rule.tags.length > 0 && (
            <Flex wrap="wrap" gap={{ initial: '1', xs: '2' }}>
              {rule.tags.slice(0, 3).map((ruleTag) => (
                <Badge
                  key={ruleTag.tag.id}
                  variant="outline"
                  style={{ borderColor: ruleTag.tag.color }}
                  className="px-2 py-1 text-xs"
                >
                  {ruleTag.tag.name}
                </Badge>
              ))}
              {rule.tags.length > 3 && (
                <Badge variant="outline" className="px-2 py-1 text-muted-foreground text-xs">
                  +{rule.tags.length - 3} more
                </Badge>
              )}
            </Flex>
          )}

          <div className="text-muted-foreground text-xs">
            Updated {new Date(rule.updatedAt).toLocaleDateString()}
          </div>
        </div>
      </Card>

      <Dialog.Root open={showContent} onOpenChange={setShowContent}>
        <Dialog.Content className="max-h-[90vh] w-[95vw] xs:w-[90vw] max-w-4xl overflow-hidden">
          <div className="space-y-2">
            <Dialog.Title className="truncate text-lg xs:text-xl">{rule.title}</Dialog.Title>
            {rule.description && (
              <Dialog.Description className="text-sm xs:text-base">
                {rule.description}
              </Dialog.Description>
            )}
          </div>
          <div className="flex-1 overflow-hidden">
            <CodeEditor value={rule.content} onChange={() => {}} className="h-[50vh] xs:h-[60vh]" />
          </div>
        </Dialog.Content>
      </Dialog.Root>
    </>
  );
}
