'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Flex, Heading, Text } from '@radix-ui/themes';
import { Copy, Download, Eye, User } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { CodeEditor } from '@/components/ui/code-editor';
import { useRule } from '@/hooks/use-rule-queries';
import type { RulesetRule } from '@/hooks/use-ruleset-queries';

interface RulesetContentViewerProps {
  rules: RulesetRule[];
  selectedRuleId: string | 'ALL';
  rulesetName: string;
}

export function RulesetContentViewer({
  rules,
  selectedRuleId,
  rulesetName,
}: RulesetContentViewerProps) {
  const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});

  const selectedRulesetRule =
    selectedRuleId !== 'ALL' ? rules.find(({ rule }) => rule.id === selectedRuleId) : null;

  // Fetch full rule data when a specific rule is selected
  const { data: fullRule, isLoading: ruleLoading } = useRule(
    selectedRuleId !== 'ALL' ? selectedRuleId : ''
  );

  const handleCopyContent = async (content: string, ruleId?: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast.success('Content copied to clipboard');

      if (ruleId) {
        setCopiedStates((prev) => ({ ...prev, [ruleId]: true }));
        setTimeout(() => {
          setCopiedStates((prev) => ({ ...prev, [ruleId]: false }));
        }, 2000);
      }
    } catch (_error) {
      toast.error('Failed to copy content');
    }
  };

  const handleDownloadMDC = async () => {
    if (!fullRule) return;

    try {
      const blob = new Blob([fullRule.content], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${fullRule.title.toLowerCase().replace(/\s+/g, '-')}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('Rule downloaded');
    } catch (_error) {
      toast.error('Failed to download rule');
    }
  };

  // Generate ruleset content using similar logic to the download API
  const generateAllRulesContent = () => {
    const date = new Date().toLocaleDateString();
    const ruleCount = rules.length;

    // Get all unique tags from rules in the ruleset
    const allTags = rules.flatMap(({ rule }) => rule.tags.map(({ tag }) => tag));
    const uniqueTags = allTags.filter(
      (tag, index, self) => index === self.findIndex((t) => t.id === tag.id)
    );
    const tags = uniqueTags.map((t) => t.name).join(', ');

    // Generate ruleset header with metadata (similar to generateRulesetMDXContent)
    const rulesetHeader = `---
title: "${rulesetName}"
description: "Ruleset overview with ${ruleCount} rules"
date: "${date}"
ruleCount: ${ruleCount}
tags: [${uniqueTags.map((t) => `"${t.name}"`).join(', ')}]
type: "ruleset"
---

# ${rulesetName}

This document contains all rules from the "${rulesetName}" ruleset.

**Rules:** ${ruleCount}
${tags ? `**Tags:** ${tags}` : ''}

## Rules in this Ruleset

`;

    // Generate individual rule content (simplified version since we don't have full rule content)
    const rulesContent = rules
      .map(({ rule }, index) => {
        const ruleHeader = `### ${index + 1}. ${rule.title}\n\n`;
        const ruleDescription = rule.description ? `> ${rule.description}\n\n` : '';
        const ruleTags =
          rule.tags.length > 0
            ? `**Tags:** ${rule.tags.map(({ tag }) => tag.name).join(', ')}\n\n`
            : '';
        const ruleNote = `*Note: Full rule content available when viewing individual rules.*\n\n`;

        return ruleHeader + ruleDescription + ruleTags + ruleNote;
      })
      .join('\n---\n\n');

    return rulesetHeader + rulesContent;
  };

  const handleDownloadAllRules = async () => {
    try {
      const allContent = generateAllRulesContent();
      const blob = new Blob([allContent], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      // Use similar filename format as the download API
      const sanitizedName = rulesetName
        .replace(/[^a-z0-9]/gi, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '')
        .toLowerCase()
        .substring(0, 100);
      a.download = `${sanitizedName}-ruleset.mdx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('Ruleset overview downloaded');
    } catch (_error) {
      toast.error('Failed to download ruleset overview');
    }
  };

  if (selectedRuleId === 'ALL') {
    const allContent = generateAllRulesContent();

    return (
      <Box className="flex h-full flex-col">
        <Box p="4" className="border-b bg-white dark:bg-gray-950">
          <Flex justify="between" align="center">
            <Flex direction="column" gap="1">
              <Heading size="4">All Rules Combined</Heading>
              <Text size="2" color="gray">
                {rules.length} rules from "{rulesetName}"
              </Text>
            </Flex>
            <Flex gap="2">
              <Button variant="soft" size="2" onClick={() => handleCopyContent(allContent)}>
                <Copy className="mr-2 h-4 w-4" />
                Copy All
              </Button>
              <Button variant="soft" size="2" onClick={handleDownloadAllRules}>
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
            </Flex>
          </Flex>
        </Box>

        <Box className="flex-1 overflow-hidden">
          <CodeEditor value={allContent} language="markdown" readOnly className="h-full" />
        </Box>
      </Box>
    );
  }

  if (!selectedRulesetRule) {
    return (
      <Box className="flex h-full items-center justify-center">
        <Flex direction="column" align="center" gap="3">
          <Eye className="h-12 w-12 text-gray-400" />
          <Text size="4" color="gray">
            Select a rule to view its content
          </Text>
        </Flex>
      </Box>
    );
  }

  if (ruleLoading) {
    return (
      <Box className="flex h-full items-center justify-center">
        <Flex direction="column" align="center" gap="3">
          <div className="h-8 w-8 animate-spin rounded-full border-blue-600 border-b-2"></div>
          <Text size="3" color="gray">
            Loading rule content...
          </Text>
        </Flex>
      </Box>
    );
  }

  if (!fullRule) {
    return (
      <Box className="flex h-full items-center justify-center">
        <Flex direction="column" align="center" gap="3">
          <Eye className="h-12 w-12 text-gray-400" />
          <Text size="4" color="gray">
            Failed to load rule content
          </Text>
        </Flex>
      </Box>
    );
  }

  return (
    <Box className="flex h-full flex-col">
      <Box p="4" className="border-b bg-white dark:bg-gray-950">
        <Flex justify="between" align="start" gap="4">
          <Flex direction="column" gap="2" className="flex-1">
            <Heading size="4">{fullRule.title}</Heading>
            {fullRule.description && (
              <Text size="2" color="gray">
                {fullRule.description}
              </Text>
            )}

            <Flex align="center" gap="4" wrap="wrap">
              <Flex align="center" gap="1">
                <User className="h-3 w-3 text-gray-500" />
                <Text size="1" color="gray">
                  {fullRule.user?.name || fullRule.user?.email || 'Unknown'}
                </Text>
              </Flex>
              <Text size="1" color="gray">
                Updated {new Date(fullRule.updatedAt).toLocaleDateString()}
              </Text>
            </Flex>

            {fullRule.tags.length > 0 && (
              <Flex gap="1" wrap="wrap">
                {fullRule.tags.map(({ tag }) => (
                  <Badge
                    key={tag.id}
                    variant="soft"
                    size="1"
                    style={{ backgroundColor: `${tag.color}20`, color: tag.color }}
                  >
                    {tag.name}
                  </Badge>
                ))}
              </Flex>
            )}
          </Flex>

          <Flex gap="2">
            <Button
              variant="soft"
              size="2"
              onClick={() => handleCopyContent(fullRule.content, fullRule.id)}
            >
              <Copy className="mr-2 h-4 w-4" />
              {copiedStates[fullRule.id] ? 'Copied!' : 'Copy'}
            </Button>
            <Button variant="soft" size="2" onClick={handleDownloadMDC}>
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
          </Flex>
        </Flex>
      </Box>

      <Box className="flex-1 overflow-hidden">
        <CodeEditor value={fullRule.content} language="markdown" readOnly className="h-full" />
      </Box>
    </Box>
  );
}
