'use client';

import { Theme } from '@radix-ui/themes';
import type { ThemeProviderProps } from 'next-themes';
import { ThemeProvider as NextThemesProvider, useTheme } from 'next-themes';
import * as React from 'react';

function RadixThemeWrapper({ children }: { children: React.ReactNode }) {
  const { resolvedTheme, theme, systemTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  // Safely get theme with fallback
  const _theme = theme || 'dark';
  const _systemTheme = systemTheme || 'dark';

  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Use light theme as default and during hydration
  const appearance = mounted ? (resolvedTheme as 'light' | 'dark') || 'light' : 'light';

  // Apply theme class to document element for global CSS variable access
  React.useEffect(() => {
    if (typeof document !== 'undefined') {
      // Update the document class when theme changes
      const currentClass = `radix-themes ${appearance}`;
      document.documentElement.className = currentClass;
    }
  }, [appearance]);

  return (
    <Theme
      accentColor="blue"
      grayColor="slate"
      radius="medium"
      scaling="100%"
      appearance={appearance}
      panelBackground="translucent"
      hasBackground={true}
    >
      {children}
    </Theme>
  );
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      defaultTheme="light"
      enableSystem={true}
      attribute="class"
      disableTransitionOnChange={false}
      {...props}
    >
      <RadixThemeWrapper>{children}</RadixThemeWrapper>
    </NextThemesProvider>
  );
}
