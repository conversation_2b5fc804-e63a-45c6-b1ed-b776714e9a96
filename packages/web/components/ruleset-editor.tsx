'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Select, <PERSON>Area, TextField } from '@radix-ui/themes';
import { Eye, Loader2, Lock, Search } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useRules } from '@/hooks/use-rule-queries';
import type { Rule } from '@/lib/store';

interface RulesetRule {
  order: number;
  rule: Rule;
}

interface Ruleset {
  id?: string;
  name: string;
  description: string | null;
  visibility: 'PUBLIC' | 'PRIVATE';
  rules: RulesetRule[];
}

interface RulesetEditorProps {
  initialRuleset?: Ruleset;
  onSave: (ruleset: Omit<Ruleset, 'id' | 'rules'> & { ruleIds: string[] }) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

// Helper function to validate form data
function validateFormData(name: string, selectedRuleIds: string[]): string | null {
  if (!name.trim()) {
    return 'Ruleset name is required';
  }

  if (selectedRuleIds.length === 0) {
    return 'Please select at least one rule';
  }

  return null;
}

// Helper function to filter rules based on search term
function filterRules(rules: Rule[], searchTerm: string): Rule[] {
  return rules.filter(
    (rule) =>
      rule.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.tags.some((t) => t.tag.name.toLowerCase().includes(searchTerm.toLowerCase()))
  );
}

export function RulesetEditor({
  initialRuleset,
  onSave,
  onCancel,
  isLoading = false,
}: RulesetEditorProps) {
  const [name, setName] = useState(initialRuleset?.name || '');
  const [description, setDescription] = useState(initialRuleset?.description || '');
  const [visibility, setVisibility] = useState<'PUBLIC' | 'PRIVATE'>(
    initialRuleset?.visibility || 'PRIVATE'
  );
  const [selectedRuleIds, setSelectedRuleIds] = useState<string[]>(
    initialRuleset?.rules.map((r) => r.rule.id) || []
  );

  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Fetch available rules using React Query
  const { data: rulesResponse, isLoading: rulesLoading, error: rulesError } = useRules();

  // Handle query errors
  useEffect(() => {
    if (rulesError) {
      console.error('Error fetching rules:', rulesError);
      setError('Failed to load rules');
    }
  }, [rulesError]);

  const rules = rulesResponse?.rules || [];
  const filteredRules = filterRules(rules, searchTerm);

  const handleRuleToggle = (ruleId: string) => {
    setSelectedRuleIds((prev) =>
      prev.includes(ruleId) ? prev.filter((id) => id !== ruleId) : [...prev, ruleId]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = validateFormData(name, selectedRuleIds);
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setError(null);
      await onSave({
        name: name.trim(),
        description: description.trim() || null,
        visibility,
        ruleIds: selectedRuleIds,
      });
    } catch (error) {
      console.error('Error saving ruleset:', error);
      setError('Failed to save ruleset');
    }
  };

  return (
    <div className="mx-auto max-w-4xl space-y-6 p-6">
      <Card>
        <div className="p-6">
          <h2 className="mb-2 font-bold text-2xl">
            {initialRuleset ? 'Edit Ruleset' : 'Create New Ruleset'}
          </h2>
          <p className="mb-6 text-gray-600">
            Create a collection of AI rules for quick setup and deployment
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="rounded-md border border-red-200 bg-red-50 p-4">
                <p className="text-red-800 text-sm">{error}</p>
              </div>
            )}

            {/* Basic Info */}
            <div className="space-y-4">
              <div>
                <label htmlFor="ruleset-name" className="mb-2 block font-medium text-sm">
                  Ruleset Name
                </label>
                <TextField.Root
                  id="ruleset-name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter ruleset name..."
                  disabled={isLoading}
                  className="w-full"
                />
              </div>

              <div>
                <label htmlFor="ruleset-description" className="mb-2 block font-medium text-sm">
                  Description (Optional)
                </label>
                <TextArea
                  id="ruleset-description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Describe what this ruleset is for..."
                  rows={3}
                  disabled={isLoading}
                  className="w-full"
                />
              </div>

              <div>
                <div className="mb-2 block font-medium text-sm">Visibility</div>
                <Select.Root
                  value={visibility}
                  onValueChange={(value: 'PUBLIC' | 'PRIVATE') => setVisibility(value)}
                  disabled={isLoading}
                >
                  <Select.Trigger className="w-full" />
                  <Select.Content>
                    <Select.Item value="PRIVATE">
                      <div className="flex items-center gap-2">
                        <Lock className="h-4 w-4" />
                        Private - Only you can access
                      </div>
                    </Select.Item>
                    <Select.Item value="PUBLIC">
                      <div className="flex items-center gap-2">
                        <Eye className="h-4 w-4" />
                        Public - Anyone can view
                      </div>
                    </Select.Item>
                  </Select.Content>
                </Select.Root>
              </div>
            </div>

            <hr className="border-gray-200" />

            {/* Rule Selection */}
            <div className="space-y-4">
              <div>
                <div className="mb-2 block font-medium text-sm">
                  Select Rules ({selectedRuleIds.length} selected)
                </div>
                <div>
                  <TextField.Root
                    placeholder="Search rules..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                    disabled={isLoading || rulesLoading}
                  >
                    <TextField.Slot>
                      <Search className="h-4 w-4 text-gray-400" />
                    </TextField.Slot>
                  </TextField.Root>
                </div>
              </div>

              <div className="h-96 overflow-y-auto rounded-md border p-4">
                {rulesLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading rules...</span>
                  </div>
                ) : filteredRules.length === 0 ? (
                  <div className="py-8 text-center text-gray-500">
                    {searchTerm ? 'No rules found matching your search' : 'No rules available'}
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredRules.map((rule) => (
                      <div
                        key={rule.id}
                        className="flex items-start gap-3 rounded-lg border p-3 transition-colors hover:bg-gray-50"
                      >
                        <input
                          type="checkbox"
                          id={rule.id}
                          checked={selectedRuleIds.includes(rule.id)}
                          onChange={() => handleRuleToggle(rule.id)}
                          disabled={isLoading}
                          className="mt-1"
                        />
                        <div className="min-w-0 flex-1">
                          <label
                            htmlFor={rule.id}
                            className="mb-1 block cursor-pointer font-medium"
                          >
                            {rule.title}
                          </label>
                          {rule.description && (
                            <p className="mb-2 line-clamp-2 text-gray-600 text-sm">
                              {rule.description}
                            </p>
                          )}
                          <div className="flex flex-wrap items-center gap-2">
                            {rule.visibility === 'PUBLIC' ? (
                              <Badge color="green" variant="soft" className="text-xs">
                                <Eye className="mr-1 h-3 w-3" />
                                Public
                              </Badge>
                            ) : (
                              <Badge color="gray" variant="soft" className="text-xs">
                                <Lock className="mr-1 h-3 w-3" />
                                Private
                              </Badge>
                            )}
                            {rule.tags.slice(0, 3).map(({ tag }) => (
                              <Badge
                                key={tag.id}
                                variant="soft"
                                className="text-xs"
                                style={{ backgroundColor: `${tag.color}20`, color: tag.color }}
                              >
                                {tag.name}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3">
              <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !name.trim() || selectedRuleIds.length === 0}
                variant="solid"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {initialRuleset ? 'Update Ruleset' : 'Create Ruleset'}
              </Button>
            </div>
          </form>
        </div>
      </Card>
    </div>
  );
}
