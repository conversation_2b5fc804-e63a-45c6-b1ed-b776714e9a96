'use client';

import { javascript } from '@codemirror/lang-javascript';
import { json } from '@codemirror/lang-json';
import { markdown } from '@codemirror/lang-markdown';
import { xml } from '@codemirror/lang-xml';
import { EditorState } from '@codemirror/state';
import { oneDark } from '@codemirror/theme-one-dark';
import { EditorView } from '@codemirror/view';
import CodeMirror from '@uiw/react-codemirror';
import { useTheme } from 'next-themes';

interface CodeEditorProps {
  value: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  language?: 'javascript' | 'markdown' | 'json' | 'xml';
  readOnly?: boolean;
}

export function CodeEditor({
  value,
  onChange,
  placeholder,
  className,
  language = 'javascript',
  readOnly = false,
}: CodeEditorProps) {
  const { theme } = useTheme();

  const getLanguageExtension = () => {
    switch (language) {
      case 'markdown':
        return markdown();
      case 'json':
        return json();
      case 'xml':
        return xml();
      default:
        return javascript();
    }
  };

  const extensions = [
    getLanguageExtension(),
    EditorView.theme({
      '&': {
        fontSize: '14px',
      },
      '.cm-content': {
        padding: '16px',
        minHeight: '200px',
      },
      '.cm-focused': {
        outline: 'none',
      },
      '.cm-editor': {
        borderRadius: '8px',
      },
      '&.cm-editor.cm-readonly .cm-content': {
        backgroundColor: readOnly ? (theme === 'dark' ? '#1a1a1a' : '#f8f9fa') : 'inherit',
      },
    }),
  ];

  if (readOnly) {
    extensions.push(EditorState.readOnly.of(true));
  }

  return (
    <div className={className}>
      <CodeMirror
        value={value}
        onChange={onChange ? (val) => onChange(val) : undefined}
        placeholder={placeholder}
        theme={theme === 'dark' ? oneDark : undefined}
        extensions={extensions}
        readOnly={readOnly}
        basicSetup={{
          lineNumbers: true,
          foldGutter: true,
          dropCursor: false,
          allowMultipleSelections: false,
          indentOnInput: !readOnly,
          bracketMatching: true,
          closeBrackets: !readOnly,
          autocompletion: !readOnly,
          highlightSelectionMatches: false,
        }}
      />
    </div>
  );
}
