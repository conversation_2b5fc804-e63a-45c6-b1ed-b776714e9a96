'use client';

import { <PERSON><PERSON>, <PERSON>, Flex, Text, TextField, Theme } from '@radix-ui/themes';
import { Moon, Sun } from 'lucide-react';
import { useState } from 'react';

export function IntegrationTest() {
  const [isDark, setIsDark] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <Theme appearance={isDark ? 'dark' : 'light'}>
      <div className="min-h-screen bg-background p-8">
        <div className="mx-auto max-w-4xl space-y-8">
          {/* Header */}
          <Flex justify="between" align="center">
            <Text size="6" className="font-bold">
              Tailwind + Radix Integration Test
            </Text>
            <Button
              variant="ghost"
              size="2"
              onClick={() => setIsDark(!isDark)}
              className="hover:bg-accent"
            >
              {isDark ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            </Button>
          </Flex>

          {/* Color Palette Test */}
          <Card className="p-6">
            <Text size="4" className="mb-4 font-semibold">
              Color System Test
            </Text>
            <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
              <div className="rounded-md bg-primary p-4 text-primary-foreground">Primary</div>
              <div className="rounded-md bg-secondary p-4 text-secondary-foreground">Secondary</div>
              <div className="rounded-md bg-accent p-4 text-accent-foreground">Accent</div>
              <div className="rounded-md bg-destructive p-4 text-destructive-foreground">
                Destructive
              </div>
            </div>
          </Card>

          {/* Responsive Layout Test */}
          <Card className="p-6">
            <Text size="4" className="mb-4 font-semibold">
              Responsive Layout
            </Text>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div className="rounded-lg border bg-card p-4">
                <Text className="font-medium">Card 1</Text>
                <Text className="mt-2 text-muted-foreground text-sm">
                  Mobile-first responsive design
                </Text>
              </div>
              <div className="rounded-lg border bg-card p-4">
                <Text className="font-medium">Card 2</Text>
                <Text className="mt-2 text-muted-foreground text-sm">
                  Grid layout with Tailwind
                </Text>
              </div>
              <div className="rounded-lg border bg-card p-4">
                <Text className="font-medium">Card 3</Text>
                <Text className="mt-2 text-muted-foreground text-sm">
                  Radix + Tailwind integration
                </Text>
              </div>
            </div>
          </Card>

          {/* Form Components Test */}
          <Card className="p-6">
            <Text size="4" className="mb-4 font-semibold">
              Form Components
            </Text>
            <Flex direction="column" gap="4">
              <TextField.Root
                placeholder="Search with Radix + Tailwind..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="placeholder:text-muted-foreground"
              />

              <Flex wrap="wrap" gap="2">
                <Button variant="solid" size="2">
                  Default Button
                </Button>
                <Button variant="soft" size="2">
                  Secondary
                </Button>
                <Button variant="outline" size="2">
                  Outline
                </Button>
                <Button variant="ghost" size="2">
                  Ghost
                </Button>
              </Flex>
            </Flex>
          </Card>

          {/* Typography Test */}
          <Card className="p-6">
            <Text size="4" className="mb-4 font-semibold">
              Typography Scale
            </Text>
            <div className="space-y-2">
              <Text size="6" className="font-bold">
                Heading 1 (size 6)
              </Text>
              <Text size="5" className="font-semibold">
                Heading 2 (size 5)
              </Text>
              <Text size="4" className="font-medium">
                Heading 3 (size 4)
              </Text>
              <Text size="3">Body text (size 3)</Text>
              <Text size="2" className="text-muted-foreground">
                Small text (size 2)
              </Text>
              <Text size="1" className="text-muted-foreground">
                Extra small text (size 1)
              </Text>
            </div>
          </Card>

          {/* Mobile Responsive Test */}
          <Card className="p-6">
            <Text size="4" className="mb-4 font-semibold">
              Mobile Responsive
            </Text>
            <div className="grid grid-cols-1 xs:grid-cols-2 gap-4 sm:grid-cols-3">
              <div className="rounded-md bg-blue-100 p-4 text-center dark:bg-blue-900">
                <Text className="text-xs sm:text-sm">Responsive</Text>
              </div>
              <div className="rounded-md bg-green-100 p-4 text-center dark:bg-green-900">
                <Text className="text-xs sm:text-sm">Design</Text>
              </div>
              <div className="rounded-md bg-purple-100 p-4 text-center dark:bg-purple-900">
                <Text className="text-xs sm:text-sm">System</Text>
              </div>
            </div>
          </Card>

          {/* Status Badge */}
          <Card className="p-6">
            <Flex justify="between" align="center">
              <Text size="4" className="font-semibold">
                Integration Status
              </Text>
              <div className="rounded-full bg-green-100 px-3 py-1 font-medium text-green-800 text-sm dark:bg-green-900 dark:text-green-200">
                ✅ Working Perfectly
              </div>
            </Flex>
            <Text className="mt-2 text-muted-foreground">
              Tailwind CSS and Radix UI are now seamlessly integrated!
            </Text>
          </Card>
        </div>
      </div>
    </Theme>
  );
}
