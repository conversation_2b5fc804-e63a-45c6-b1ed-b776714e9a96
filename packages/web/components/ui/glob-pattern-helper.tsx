'use client';

import { <PERSON>, But<PERSON>, Code, Flex, Popover, Separator, Text } from '@radix-ui/themes';
import { Check, Copy, HelpCircle } from 'lucide-react';
import { useState } from 'react';

interface GlobPatternExample {
  pattern: string;
  description: string;
  category: string;
}

const GLOB_PATTERN_EXAMPLES: GlobPatternExample[] = [
  {
    pattern: '.cursor/rules/*.md',
    description: 'Core standards',
    category: 'Standards',
  },
  {
    pattern: 'src/**/*.{js,ts}',
    description: 'Language rules',
    category: 'Languages',
  },
  {
    pattern: '**/*.test.{js,ts}',
    description: 'Testing standards',
    category: 'Testing',
  },
  {
    pattern: 'src/components/**/*.tsx',
    description: 'React components',
    category: 'Frameworks',
  },
  {
    pattern: 'docs/**/*.md',
    description: 'Documentation',
    category: 'Documentation',
  },
  {
    pattern: '*.config.{js,json}',
    description: 'Configuration files',
    category: 'Configuration',
  },
  {
    pattern: 'dist/**/*',
    description: 'Build artifacts',
    category: 'Build',
  },
  {
    pattern: 'src/**/*.{js,jsx,ts,tsx}',
    description: 'Multiple extensions',
    category: 'Advanced',
  },
  {
    pattern: '{dist/**/*,docs/**/*.md}',
    description: 'Multiple directories',
    category: 'Advanced',
  },
];

interface GlobPatternHelperProps {
  onPatternSelect?: (pattern: string) => void;
}

export function GlobPatternHelper({ onPatternSelect }: GlobPatternHelperProps) {
  const [copiedPattern, setCopiedPattern] = useState<string | null>(null);

  const handleCopyPattern = async (pattern: string) => {
    try {
      await navigator.clipboard.writeText(pattern);
      setCopiedPattern(pattern);
      if (onPatternSelect) {
        onPatternSelect(pattern);
      }
      setTimeout(() => setCopiedPattern(null), 2000);
    } catch (error) {
      console.error('Failed to copy pattern:', error);
    }
  };

  const groupedExamples = GLOB_PATTERN_EXAMPLES.reduce(
    (acc, example) => {
      if (!acc[example.category]) {
        acc[example.category] = [];
      }
      acc[example.category].push(example);
      return acc;
    },
    {} as Record<string, GlobPatternExample[]>
  );

  return (
    <Popover.Root>
      <Popover.Trigger>
        <Button variant="ghost" size="1" className="p-1">
          <HelpCircle className="h-4 w-4" />
        </Button>
      </Popover.Trigger>
      <Popover.Content className="max-h-96 w-96 overflow-y-auto">
        <Box className="space-y-4">
          <div>
            <Text size="3" weight="bold">
              Common Glob Pattern Examples
            </Text>
            <Text size="2" color="gray" className="mt-1 block">
              Click any pattern to copy it to your clipboard
            </Text>
          </div>

          <div className="space-y-4">
            {Object.entries(groupedExamples).map(([category, examples]) => (
              <div key={category}>
                <Text size="2" weight="medium" color="gray" className="mb-2 block">
                  {category}
                </Text>
                <div className="space-y-2">
                  {examples.map((example, _index) => (
                    <button
                      type="button"
                      key={example.pattern}
                      className="group w-full cursor-pointer rounded-md border border-gray-200 p-3 text-left transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:hover:bg-gray-800"
                      onClick={() => handleCopyPattern(example.pattern)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          handleCopyPattern(example.pattern);
                        }
                      }}
                      tabIndex={0}
                    >
                      <Flex justify="between" align="center" className="mb-1">
                        <Code size="2" className="font-mono">
                          {example.pattern}
                        </Code>
                        <div className="opacity-0 transition-opacity group-hover:opacity-100">
                          {copiedPattern === example.pattern ? (
                            <Check className="h-4 w-4 text-green-500" />
                          ) : (
                            <Copy className="h-4 w-4 text-gray-500" />
                          )}
                        </div>
                      </Flex>
                      <Text size="1" color="gray">
                        {example.description}
                      </Text>
                    </button>
                  ))}
                </div>
                {category !== 'Advanced' && <Separator className="my-3" />}
              </div>
            ))}
          </div>

          <Box className="border-gray-200 border-t pt-2 dark:border-gray-700">
            <Text size="1" color="gray">
              <strong>Tip:</strong> Use <Code>**</Code> for recursive matching, <Code>*</Code> for
              single level, and <Code>{'{a,b}'}</Code> for alternatives.
            </Text>
          </Box>
        </Box>
      </Popover.Content>
    </Popover.Root>
  );
}
