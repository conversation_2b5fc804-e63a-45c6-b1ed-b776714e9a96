'use client';

import { Select } from '@radix-ui/themes';
import { useState, useTransition } from 'react';
import { type Locale, localeNames, localeNamesAbbreviated } from '@/lib/i18n';

interface LanguageSwitcherProps {
  currentLocale: Locale;
}

export function LanguageSwitcher({ currentLocale }: LanguageSwitcherProps) {
  const [isPending, startTransition] = useTransition();
  const [locale, setLocale] = useState(currentLocale);

  const handleLocaleChange = (newLocale: string) => {
    setLocale(newLocale as Locale);

    startTransition(() => {
      // Set cookie and reload the page
      // biome-ignore lint/suspicious/noDocumentCookie: setting locale cookie is necessary for i18n
      document.cookie = `locale=${newLocale};path=/;max-age=${60 * 60 * 24 * 365}`;
      window.location.reload();
    });
  };

  return (
    <Select.Root value={locale} onValueChange={handleLocaleChange} disabled={isPending}>
      <Select.Trigger
        className="h-8 w-[60px] px-2 text-xs"
        placeholder={localeNamesAbbreviated[locale]}
      />
      <Select.Content>
        {(Object.entries(localeNamesAbbreviated) as [Locale, string][]).map(([code, abbrev]) => (
          <Select.Item key={code} value={code}>
            <span className="text-xs">{abbrev}</span>
            <span className="ml-2 text-muted-foreground text-xs">{localeNames[code]}</span>
          </Select.Item>
        ))}
      </Select.Content>
    </Select.Root>
  );
}
