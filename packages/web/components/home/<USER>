'use client';

import { Box, Container, Flex, Heading, Text } from '@radix-ui/themes';
import { AlertCircle, Loader2, Star } from 'lucide-react';
import { useEffect, useState } from 'react';
import { RuleCard } from '@/components/rule-card';
import type { Rule } from '@/lib/store';

interface FeaturedRulesResponse {
  success: boolean;
  data: Rule[];
  count: number;
  message: string;
}

export function FeaturedRulesSection() {
  const [rules, setRules] = useState<Rule[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedRules = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/rules/docs');
        const data: FeaturedRulesResponse = await response.json();

        if (!response.ok) {
          throw new Error(data.message || 'Failed to fetch featured rules');
        }

        if (data.success && data.data) {
          // For now, show all docs rules as featured
          // In the future, this could be filtered or sorted by popularity
          setRules(data.data);
        } else {
          throw new Error(data.message || 'No featured rules available');
        }
      } catch (err) {
        console.error('Error fetching featured rules:', err);
        setError(err instanceof Error ? err.message : 'Failed to load featured rules');
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedRules();
  }, []);

  // Don't render the section if there are no rules and no error
  if (!loading && !error && rules.length === 0) {
    return null;
  }

  return (
    <Box py={{ initial: '8', xs: '9', md: '9' }}>
      <Container size="4" px={{ initial: '4', xs: '6' }}>
        <Flex direction="column" gap={{ initial: '6', xs: '8' }}>
          {/* Section Header */}
          <Flex direction="column" align="center" gap="3" className="text-center">
            <Flex align="center" gap="2">
              <Star className="h-6 w-6 fill-current text-primary" />
              <Heading
                size={{ initial: '6', xs: '7', md: '8' }}
                weight="bold"
                className="tracking-tight"
              >
                Featured Rules
              </Heading>
            </Flex>
            <Text size={{ initial: '3', xs: '4', md: '4' }} color="gray" className="max-w-2xl">
              Discover popular AI prompt rules from our community. Copy commands or content to boost
              your coding productivity.
            </Text>
          </Flex>

          {/* Content */}
          {loading && (
            <Flex justify="center" align="center" py="8">
              <Flex align="center" gap="3">
                <Loader2 className="h-5 w-5 animate-spin text-primary" />
                <Text size="3" color="gray">
                  Loading featured rules...
                </Text>
              </Flex>
            </Flex>
          )}

          {error && (
            <Flex justify="center" align="center" py="8">
              <Flex direction="column" align="center" gap="3" className="text-center">
                <AlertCircle className="h-8 w-8 text-red-500" />
                <Text size="3" color="red">
                  {error}
                </Text>
                <Text size="2" color="gray">
                  Please try refreshing the page or check back later.
                </Text>
              </Flex>
            </Flex>
          )}

          {!loading && !error && rules.length > 0 && (
            <div className="grid gap-4 xs:gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              {rules.map((rule) => (
                <RuleCard key={rule.id} rule={rule} isOwner={false} />
              ))}
            </div>
          )}

          {/* Call to Action */}
          {!loading && !error && rules.length > 0 && (
            <Flex justify="center" pt="4">
              <Text size="2" color="gray" className="text-center">
                Want to see more rules?{' '}
                <a href="/templates" className="font-medium text-primary hover:underline">
                  Browse our template library
                </a>
              </Text>
            </Flex>
          )}
        </Flex>
      </Container>
    </Box>
  );
}
