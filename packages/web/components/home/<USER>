'use client';

import { useLingui } from '@lingui/react';
import { Button, Flex } from '@radix-ui/themes';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';

export function HeroSection() {
  const { i18n } = useLingui();

  return (
    <section className="relative overflow-hidden py-16 xs:py-24 md:py-32">
      <div className="mobile-container relative z-10">
        <div className="mx-auto max-w-5xl text-center">
          <h1 className="mb-4 xs:mb-6 font-bold text-3xl xs:text-4xl tracking-tight md:text-6xl">
            {i18n._('hero.title')}
          </h1>
          <p className="mb-6 xs:mb-8 px-4 text-base text-muted-foreground xs:text-lg md:text-xl">
            {i18n._('hero.subtitle')}
          </p>
          <Flex
            direction={{ initial: 'column', sm: 'row' }}
            gap={{ initial: '3', xs: '4' }}
            justify={{ sm: 'center' }}
            className="w-full xs:w-auto"
          >
            <Button size="3" asChild className="touch-target w-full xs:w-auto">
              <Link href="/auth/signup" className="text-sm xs:text-base">
                {i18n._('hero.getStarted')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button size="3" variant="outline" asChild className="touch-target w-full xs:w-auto">
              <Link href="/templates" className="text-sm xs:text-base">
                {i18n._('hero.browseTemplates')}
              </Link>
            </Button>
          </Flex>
        </div>
      </div>

      {/* Background decoration */}
      <div className="-z-10 absolute inset-0 overflow-hidden">
        <div className="-translate-x-[50%] absolute top-0 left-[50%] h-[600px] xs:h-[800px] w-[600px] xs:w-[800px] rounded-full bg-primary/20 blur-[100px]" />
      </div>
    </section>
  );
}
