import { Box, Container, Flex, Heading, Text } from '@radix-ui/themes';
import { Star } from 'lucide-react';
import { RuleCard } from '@/components/rule-card';
import type { Rule } from '@/lib/store';

interface FeaturedRulesSectionStaticProps {
  rules: Rule[];
}

/**
 * Server-side FeaturedRulesSection for static generation
 * This component receives pre-fetched data for better SEO and performance
 */
export function FeaturedRulesSectionStatic({ rules }: FeaturedRulesSectionStaticProps) {
  // Fetch rules at build time
  let rules: Rule[] = [];

  try {
    rules = getAllDocsRules();
    // Limit to first 6 rules for the homepage
    rules = rules.slice(0, 6);
  } catch (error) {
    console.error('Error fetching featured rules for static generation:', error);
    // Return empty array on error - component will handle gracefully
    rules = [];
  }

>>>>>>> 0aae317 (fix; if x)
  // Don't render the section if there are no rules
  if (rules.length === 0) {
    return null;
  }

  return (
    <Box py={{ initial: '8', xs: '9', md: '9' }}>
      <Container size="4" px={{ initial: '4', xs: '6' }}>
        <Flex direction="column" gap={{ initial: '6', xs: '8' }}>
          {/* Section Header */}
          <Flex direction="column" align="center" gap="3" className="text-center">
            <Flex align="center" gap="2">
              <div className="flex items-center">
                <Star className="h-6 w-6 fill-current text-primary" />
              </div>
              <Heading
                size={{ initial: '6', xs: '7', md: '8' }}
                weight="bold"
                className="tracking-tight"
              >
                Featured Rules
              </Heading>
            </Flex>
            <Text size={{ initial: '3', xs: '4', md: '4' }} color="gray" className="max-w-2xl">
              Discover popular AI prompt rules from our community. Copy commands or content to boost
              your coding productivity.
            </Text>
          </Flex>

          {/* Rules Grid */}
          <div className="grid gap-4 xs:gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {rules.map((rule) => (
              <RuleCard key={rule.id} rule={rule} isOwner={false} />
            ))}
          </div>

          {/* Call to Action */}
          <Flex justify="center" pt="4">
            <Text size="2" color="gray" className="text-center">
              Want to see more rules?{' '}
              <a href="/templates" className="font-medium text-primary hover:underline">
                Browse our template library
              </a>
            </Text>
          </Flex>
        </Flex>
      </Container>
    </Box>
  );
}
