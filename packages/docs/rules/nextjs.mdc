
---
title: "Frontend Development Rules"
description: "How to write nextjs app"
author: "Windsurf AI"

tags: ["Nextjs"]
---


# General Code Style & Formatting
- Follow the Airbnb Style Guide for code formatting.
- Use PascalCase for React component file names (e.g., UserCard.tsx, not user-card.tsx).
- Prefer named exports for components.

# Project Structure & Architecture
- Follow Next.js patterns and use the App Router.
- Correctly determine when to use server vs. client components in Next.js.

# Styling & UI
- Use Tailwind CSS for styling.
- Use Shadcn UI for components.

# Data Fetching & Forms
- Use TanStack Query (react-query) for frontend data fetching.
- Use React Hook Form for form handling.
- Use Zod for validation.

# State Management & Logic
- Use React Context for state management.

# Backend & Database
Use Prisma for database access.