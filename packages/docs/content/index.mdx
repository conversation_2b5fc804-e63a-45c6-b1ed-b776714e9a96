---
title: Welcome to OnlyRules Documentation
description: Learn how to use OnlyRules AI Prompt Management Platform
---

OnlyRules is an AI Prompt Management Platform that helps developers create, organize, and share AI prompt rules for their favorite IDEs.

## Quick Start

Get started with OnlyRules in just a few minutes:

1. **Sign up** for an account at [OnlyRules](/)
2. **Browse** existing templates and rules
3. **Create** your own custom rules
4. **Share** with the community

## Features

- 🚀 **IDE Integration** - Works with Cursor, Windsurf, GitHub Copilot, <PERSON>, and more
- 📝 **Rule Management** - Create, edit, and organize your AI prompt rules
- 🌐 **Community Sharing** - Share and discover rules from other developers
- 🎨 **Template System** - Use pre-built templates to get started quickly
- 🔍 **Search & Discovery** - Find the perfect rules for your use case

## Popular IDEs Supported

- [Cursor](/docs/ides/cursor)
- [Windsurf](/docs/ides/windsurf)
- [GitHub Copilot](/docs/ides/github-copilot)
- [<PERSON>](/docs/ides/claude)
- [Cline](/docs/ides/cline)
- [Augment Code](/docs/ides/augment)

## Getting Help

- Check out our [API Documentation](/docs/api)
- Learn about [SEO Setup](/docs/guides/seo-setup)
- Understand [Internationalization](/docs/guides/i18n)
- Explore [Radix UI Theme Guide](/docs/guides/radix-ui-theme)

## Contributing

OnlyRules is open source! Check out our [GitHub repository](https://github.com/ranglang/onlyrules) to contribute.
