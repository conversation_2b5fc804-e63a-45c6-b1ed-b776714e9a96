---
title: Radix UI Theme v3 Guide
description: Learn how to use Radix UI Theme v3 in the OnlyRules project
---

# Radix UI Theme v3 Guide

This guide explains how to use Radix UI Theme v3 in the OnlyRules project.

## Overview

We've migrated from a mixed Tailwind/custom CSS approach to using Radix UI Theme v3's design system. This provides:

- Consistent design tokens
- Built-in dark mode support
- Accessible components
- Responsive design patterns
- Better performance

## Key Changes

### 1. Component Usage

Instead of using HTML elements with Tailwind classes, use Radix UI components:

```tsx
// ❌ Old approach
<div className="flex flex-col gap-4">
  <h1 className="text-4xl font-bold">Title</h1>
  <p className="text-gray-600">Description</p>
</div>

// ✅ New approach
<Flex direction="column" gap="4">
  <Heading size="8">Title</Heading>
  <Text color="gray">Description</Text>
</Flex>
```

### 2. Layout Components

Use Radix UI's layout components for structure:

- `Box` - Generic container
- `Flex` - Flexbox container
- `Grid` - CSS Grid container
- `Container` - Centered container with max-width
- `Section` - Page sections with consistent spacing

### 3. Responsive Design

Use the responsive object syntax:

```tsx
<Flex 
  direction={{ initial: 'column', md: 'row' }}
  gap={{ initial: '2', sm: '4', md: '6' }}
>
  {/* Content */}
</Flex>
```

### 4. Theme Configuration

Theme settings are centralized in `/lib/theme-config.ts`:

```tsx
import { themeConfig } from '@/lib/theme-config';
```

### 5. Color System

Use semantic color values:

```tsx
<Text color="gray">Muted text</Text>
<Badge color="green">Success</Badge>
<Button color="red" variant="soft">Danger</Button>
```

### 6. Typography

Use typography components with size scales:

```tsx
<Heading size="9">Hero Title</Heading>     // 60px
<Heading size="6">Section Title</Heading>  // 24px
<Text size="3">Body text</Text>           // 16px
<Text size="2" color="gray">Small</Text>  // 14px
```

### 7. Spacing

Use the spacing scale (1-9):

```tsx
<Box p="4" mt="6" mb="2">
  {/* padding: 16px, margin-top: 32px, margin-bottom: 8px */}
</Box>
```

### 8. Common Patterns

#### Cards
```tsx
<Card variant="surface" size="3">
  <Heading size="4">Card Title</Heading>
  <Text color="gray">Card content</Text>
</Card>
```

#### Forms
```tsx
<Flex direction="column" gap="4">
  <Box>
    <Text as="label" size="2" weight="medium">
      Label
    </Text>
    <TextField.Root placeholder="Enter value" />
  </Box>
  <Button>Submit</Button>
</Flex>
```

#### Dialogs
```tsx
<Dialog.Root>
  <Dialog.Trigger>
    <Button>Open Dialog</Button>
  </Dialog.Trigger>
  <Dialog.Content>
    <Dialog.Title>Title</Dialog.Title>
    <Dialog.Description>Description</Dialog.Description>
    {/* Content */}
  </Dialog.Content>
</Dialog.Root>
```

## Migration Checklist

When updating a component:

1. ✅ Replace HTML elements with Radix UI components
2. ✅ Remove Tailwind utility classes
3. ✅ Use Radix UI props for styling
4. ✅ Apply responsive object syntax where needed
5. ✅ Use semantic color values
6. ✅ Follow the spacing scale
7. ✅ Test in both light and dark modes

## Resources

- [Radix UI Themes Documentation](https://www.radix-ui.com/themes/docs)
- [Component Playground](https://www.radix-ui.com/themes/playground)
- [Color System](https://www.radix-ui.com/colors)
- Test page: `/test-theme` - View examples of proper implementation
