import { loader } from 'fumadocs-core/source';
import { createMDXSource } from 'fumadocs-mdx';
import { icons } from 'lucide-react';
import { createElement } from 'react';

export const source = loader({
  baseUrl: '/docs',
  rootDir: 'docs',
  source: createMDXSource({
    files: '../docs/content/**/*.mdx',
  }),
});

// Icon mapping for navigation
export function getIcon(name: string) {
  if (name in icons) {
    return createElement(icons[name as keyof typeof icons]);
  }
  return undefined;
}
