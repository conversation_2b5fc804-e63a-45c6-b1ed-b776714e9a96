/* @tailwind base;
@tailwind components;

@tailwind utilities; */
@import 'tailwindcss';

/* Import fumadocs-ui styles */
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';

/* Custom docs styling */
.fumadocs-doc {
  max-width: none;
  padding: 2rem;
}

.fumadocs-doc h1 {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: hsl(var(--foreground));
}

.fumadocs-doc h2 {
  font-size: 1.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: hsl(var(--foreground));
}

.fumadocs-doc h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: hsl(var(--foreground));
}

.fumadocs-doc p {
  color: hsl(var(--muted-foreground));
  margin-bottom: 1rem;
  line-height: 1.625;
}

.fumadocs-doc a {
  color: hsl(var(--primary));
  text-decoration: none;
}

.fumadocs-doc a:hover {
  text-decoration: underline;
}

.fumadocs-doc ul,
.fumadocs-doc ol {
  margin-bottom: 1rem;
  margin-left: 1.5rem;
}

.fumadocs-doc li {
  margin-bottom: 0.5rem;
  color: hsl(var(--muted-foreground));
}

.fumadocs-doc code {
  background-color: hsl(var(--muted));
  color: hsl(var(--foreground));
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.fumadocs-doc pre {
  background-color: hsl(var(--muted));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  overflow-x: auto;
}

.fumadocs-doc pre code {
  background-color: transparent;
  padding: 0;
}

.fumadocs-doc blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.fumadocs-doc table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid hsl(var(--border));
  margin: 1rem 0;
}

.fumadocs-doc th,
.fumadocs-doc td {
  border: 1px solid hsl(var(--border));
  padding: 0.5rem 1rem;
  text-align: left;
}

.fumadocs-doc th {
  background-color: hsl(var(--muted));
  font-weight: 600;
  color: hsl(var(--foreground));
}

@media (max-width: 768px) {
  .fumadocs-doc {
    padding: 1rem;
  }
  
  .fumadocs-doc h1 {
    font-size: 1.875rem;
  }
  
  .fumadocs-doc h2 {
    font-size: 1.5rem;
  }
  
  .fumadocs-doc h3 {
    font-size: 1.25rem;
  }
}
