#!/usr/bin/env node

const fs = require('node:fs');
const path = require('node:path');
const { spawn } = require('node:child_process');

// Ensure dist directory exists
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Build CSS using postcss with @tailwindcss/postcss plugin
const postcss = spawn('npx', ['postcss', 'src/styles/index.css', '-o', 'dist/index.css'], {
  stdio: 'inherit',
  cwd: __dirname,
});

postcss.on('close', (code) => {
  if (code === 0) {
    console.log('✅ CSS build completed successfully');

    // Also create minified version for production
    const minifiedBuild = spawn(
      'npx',
      ['postcss', 'src/styles/index.css', '-o', 'dist/index.min.css', '--env', 'production'],
      { stdio: 'inherit', cwd: __dirname }
    );

    minifiedBuild.on('close', (minCode) => {
      if (minCode === 0) {
        console.log('✅ Minified CSS build completed');
      } else {
        console.error('❌ Minified CSS build failed');
      }
    });
  } else {
    console.error('❌ CSS build failed');
  }
});
