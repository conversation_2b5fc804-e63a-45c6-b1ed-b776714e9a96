# State Management Data Fetching

## Metadata

```yaml
title: State Management Data Fetching
id: cmdwfdj630001jm04dehfeb4d
name: State Management Data Fetching
description: State Management Data Fetching Rules, It describe How to fetch data, and simpify logic
author: <PERSON><PERSON><PERSON>
updatedAt: 2025-08-04T01:22:56.709Z
applyType: auto
glob: *.tsx,*.ts
tags: []
```

## Rule Type

- **Type**: Project-Specific Rule
- **AI Assistant**: Tencent Cloud CodeBuddy
- **Supported IDEs**: VS Code, JetBrains IDEs

## Usage

This rule will be automatically loaded by CodeBuddy when:
- You are working in this project directory
- CodeBuddy detects the `.codebuddy` configuration


## Development Guidelines

## State Management Data Fetching

> State Management Data Fetching Rules, It describe How to fetch data, and simpify logic



### Rule Content

Use @tanstack/react-query for DataFetch , Invalidate By queryId after mutation

```
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from '@tanstack/react-query'

const queryClient = new QueryClient()

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Example />
    </QueryClientProvider>
  )
}

function Example() {
  const { isPending, error, data } = useQuery({
    queryKey: ['repoData'],
    queryFn: () =>
      fetch('https://api.github.com/repos/TanStack/query').then((res) =>
        res.json(),
      ),
  })
```

---

### CodeBuddy Integration Notes

- CodeBuddy will use these guidelines to provide context-aware code suggestions
- The AI assistant will follow these rules when generating code completions
- Use CodeBuddy's chat feature to ask questions about these guidelines
- These rules work with CodeBuddy's MCP (Model Context Protocol) integration