# OnlyRules Task Quick Reference

## 🚀 Most Common Commands

```bash
# Show all available tasks
task --list

# Start development
task dev              # or task d

# Build for production  
task build            # or task b

# Run tests
task test:run         # or task t

# Type checking
task type-check

# Lint code
task lint             # or task l
```

## 📋 Essential Workflows

### New Developer Setup
```bash
task setup
```

### Daily Development
```bash
task dev              # Start development server
task test             # Run tests in watch mode
task lint:fix         # Fix linting issues
```

### Before Committing
```bash
task pre-commit       # Run all pre-commit checks
```

### Database Operations
```bash
task db:generate      # Generate Prisma client
task db:migrate       # Run migrations
task db:studio        # Open database UI
task db:setup         # Complete database setup
```

### Build & Deploy
```bash
task build            # Production build
task deploy:verify    # Verify deployment readiness
task ci               # Run all CI checks
```

### Troubleshooting
```bash
task clean            # Clean build artifacts
task install:clean    # Clean install dependencies
task health-check     # Check project health
```

## 🔧 Utility Commands

```bash
task info             # Show project info
task format           # Format code
task i18n:update      # Update translations
task security:audit   # Security audit
task update:deps      # Check outdated packages
```

## 💡 Tips

- Use `task --summary <task-name>` for task details
- Quick aliases: `d`, `b`, `t`, `l` for common tasks
- Tasks with dependencies run automatically
- All commands respect the monorepo structure

---

**Need help?** Run `task --list` or check `TASKFILE.md` for full documentation.
