# OnlyRules Monorepo
# OnlyRules Monorepo

This is the OnlyRules AI Prompt Management Platform, now organized as a monorepo structure for better code organization and maintainability.

## Project Structure

```
packages/
├── web/                    # Next.js frontend application
│   ├── app/               # Next.js app router pages
│   ├── components/        # React components
│   ├── lib/              # Web-specific utilities
│   ├── prisma/           # Database schema and migrations
│   └── package.json      # Web package dependencies
└── shared/                # Shared utilities and types
    ├── lib/              # Shared utilities, types, and constants
    └── package.json      # Shared package dependencies
```

## Packages

### @onlyrules/web
The main Next.js application containing:
- Frontend UI components
- API routes
- Database integration with Prisma
- Authentication with better-auth
- Internationalization with Lingui

### @onlyrules/shared
Shared utilities and types used across packages:
- Common TypeScript types
- Utility functions
- Constants and enums
- Validation schemas

## Getting Started

### Prerequisites
- Node.js 18+
- npm (or Bun if available)
- PostgreSQL database
- [Task](https://taskfile.dev/) (recommended for development)

### Quick Start with Task (Recommended)

This project uses [Task](https://taskfile.dev/) as a centralized command runner for all development operations.

1. Install Task:
```bash
# macOS
brew install go-task/tap/go-task

# Or download from https://github.com/go-task/task/releases
```

2. Clone and setup:
```bash
git clone https://github.com/ranglang/onlyrules.codes.git
cd onlyrules.codes
task setup  # Complete project setup
task dev    # Start development server
```

3. Common commands:
```bash
task --list        # Show all available tasks
task dev           # Start development server
task build         # Build for production
task test:run      # Run tests
task db:studio     # Open database UI
```

See [TASK_QUICK_REFERENCE.md](TASK_QUICK_REFERENCE.md) for more commands.

### Manual Installation

1. Clone the repository:
```bash
git clone https://github.com/ranglang/onlyrules.codes.git
cd onlyrules.codes
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp packages/web/.env.example packages/web/.env.local
# Edit packages/web/.env.local with your configuration
```

4. Set up the database:
```bash
npm run db:migrate
```

5. Start the development server:
```bash
npm run dev
```

## Available Scripts

### Using Task (Recommended)

We recommend using [Task](https://taskfile.dev/) for a better development experience:

```bash
task --list        # Show all available tasks
task dev           # Start development server
task build         # Build for production
task test:run      # Run tests
task lint          # Run linting
task db:studio     # Open database UI
task setup         # Complete project setup
```

See [TASKFILE.md](TASKFILE.md) for complete documentation and [TASK_QUICK_REFERENCE.md](TASK_QUICK_REFERENCE.md) for quick commands.

### Using npm Scripts

All npm scripts are run from the root directory and automatically target the appropriate package:

#### Development
- `npm run dev` - Start the development server
- `npm run build` - Build the application for production
- `npm run start` - Start the production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

#### Testing
- `npm run test` - Run tests
- `npm run test:ui` - Run tests with UI
- `npm run test:run` - Run tests once
- `npm run test:coverage` - Run tests with coverage

### Database
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema changes to database
- `npm run db:studio` - Open Prisma Studio
- `npm run db:migrate` - Run database migrations
- `npm run db:migrate:deploy` - Deploy migrations to production
- `npm run db:migrate:reset` - Reset database and run migrations
- `npm run db:seed` - Seed the database

### Internationalization
- `npm run lingui:extract` - Extract translatable strings
- `npm run lingui:compile` - Compile translations

### Maintenance
- `npm run install:clean` - Clean install all dependencies

## Monorepo Benefits

1. **Code Sharing**: Shared utilities and types are centralized in the `@onlyrules/shared` package
2. **Consistent Dependencies**: All packages use the same versions of shared dependencies
3. **Simplified Development**: Single repository for all related code
4. **Better Organization**: Clear separation between frontend, shared utilities, and potential future packages
5. **Scalability**: Easy to add new packages (mobile app, CLI tools, etc.)

## Development Workflow

### Adding Shared Utilities
1. Add new utilities to `packages/shared/lib/`
2. Export them from `packages/shared/lib/index.ts`
3. Import them in other packages using `@onlyrules/shared`

### Working with the Web Package
1. Navigate to `packages/web/` for web-specific development
2. All existing Next.js development workflows remain the same
3. Use shared utilities by importing from `@onlyrules/shared`

## Deployment

The deployment process remains the same as before. The monorepo structure is transparent to the deployment pipeline:

1. The main application is in `packages/web/`
2. All build and deployment scripts target the web package
3. Shared dependencies are automatically included during the build process

## Migration Notes

This refactoring maintains 100% backward compatibility:
- All existing functionality is preserved
- API routes and database schema remain unchanged
- Environment variables and configuration stay the same
- Build and deployment processes are unchanged

The only changes are:
- Code is now organized in packages
- Shared utilities are available from `@onlyrules/shared`
- Scripts now delegate to the appropriate package

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes in the appropriate package
4. Add tests if applicable
5. Submit a pull request

For more detailed information about each package, see the README files in the respective package directories.
